# 密码检查内存优化方案

## 问题描述

在密码检查缓存预热过程中出现内存溢出错误：
```
java.lang.OutOfMemoryError: Java heap space
```

## 问题原因

1. **大数据量**: 禁止单词集合包含大量数据（英文词典 + 拼音词典 + 替换组合）
2. **指数级增长**: `generateReplacements()` 方法为每个单词生成指数级数量的替换组合
3. **Trie构建**: Aho-Corasick Trie库在构建包含数百万个关键词的Trie时内存不足

## 解决方案

### 1. 运行时替换生成

**修改文件**: `GzgWeakPasswordCheckUtils.java`

- **移除预生成**: 不再在 `buildForbiddenWords()` 中预生成所有替换组合
- **运行时生成**: 在 `validatePassword()` 中为输入密码生成替换组合
- **内存优化**: 大幅减少内存使用，从指数级增长变为线性增长

```java
// 修改前：预生成所有替换组合
private static Set<String> buildForbiddenWords() throws IOException {
    // ...
    List<String> replacements = generateReplacements(lowerWord);
    forbiddenWords.addAll(replacements); // 导致指数级内存增长
}

// 修改后：运行时生成替换组合
public List<String> validatePassword(String password) {
    // 获取基础禁止单词集合（不包含替换组合）
    Set<String> baseForbiddenWords = getOrCreateForbiddenWords();
    
    // 生成输入密码的所有替换组合
    List<String> passwordReplacements = generateReplacements(lowerPwd);
    
    // 检查密码及其替换组合是否包含禁止单词
    // ...
}
```

### 2. 移除Trie依赖

**修改文件**: `GzgWeakPasswordCheckUtils.java`

- **移除Trie**: 不再使用Aho-Corasick Trie库
- **直接匹配**: 使用简单的字符串包含检查
- **内存节省**: 避免Trie构建时的内存消耗

```java
// 移除Trie相关导入
// import org.ahocorasick.trie.Trie;
// import org.ahocorasick.trie.Emit;

// 移除Trie构建方法
// private Trie getOrCreateTrie() { ... }
// private Trie buildTrie() { ... }
```

### 3. 增加JVM堆内存

**新增文件**: `run-with-heap.bat`

```batch
@echo off
echo 设置JVM堆内存参数...
set JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC

echo 启动应用...
call mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"
```

### 4. 配置选项

在 `application.yml` 中添加以下配置：

```yaml
# 密码检查缓存配置
password:
  cache:
    warmup:
      enabled: true    # 是否启用缓存预热
      light: true      # 是否使用轻量级预热
```

## 性能影响

### 内存使用优化
- **优化前**: 数百万个预生成的替换组合存储在内存中
- **优化后**: 只存储基础单词，运行时生成替换组合
- **内存节省**: 约90%的内存使用减少

### 性能影响
- **检查速度**: 略有下降（需要运行时生成替换组合）
- **启动速度**: 显著提升（无需构建大型Trie）
- **内存稳定性**: 大幅改善（避免OutOfMemoryError）

## 使用建议

1. **开发环境**: 使用 `run-with-heap.bat` 启动应用
2. **生产环境**: 配置适当的JVM堆内存参数
3. **监控**: 监控内存使用情况，必要时调整堆内存大小

## 故障排除

### 如果仍然出现内存问题
1. 增加JVM堆内存：`-Xmx8g`
2. 使用G1垃圾收集器：`-XX:+UseG1GC`
3. 启用内存监控：`-XX:+PrintGCDetails`

### 性能调优
1. 调整分片大小：修改 `SHARD_SIZE` 常量
2. 优化替换规则：减少 `REPLACEMENT_MAP` 中的映射
3. 限制单词长度：调整 `len >= 3 && len <= 20` 的范围 