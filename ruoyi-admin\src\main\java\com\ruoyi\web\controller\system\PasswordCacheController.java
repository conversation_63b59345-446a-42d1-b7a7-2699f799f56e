package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.password.GzgWeakPasswordCheckUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 密码缓存管理控制器
 */
@RestController
@RequestMapping("/system/password-cache")
public class PasswordCacheController extends BaseController {

    @Autowired
    private GzgWeakPasswordCheckUtils passwordCheckUtils;

    /**
     * 预热密码检查缓存
     */
    @PreAuthorize("@ss.hasPermi('system:password:cache')")
    @Log(title = "密码缓存管理", businessType = BusinessType.OTHER)
    @PostMapping("/warmup")
    public AjaxResult warmUpCache() {
        try {
            passwordCheckUtils.warmUpCache();
            return success("密码检查缓存预热成功");
        } catch (Exception e) {
            return error("密码检查缓存预热失败: " + e.getMessage());
        }
    }

    /**
     * 清除密码检查缓存
     */
    @PreAuthorize("@ss.hasPermi('system:password:cache')")
    @Log(title = "密码缓存管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clear")
    public AjaxResult clearCache() {
        try {
            passwordCheckUtils.clearPasswordCache();
            return success("密码检查缓存清除成功");
        } catch (Exception e) {
            return error("密码检查缓存清除失败: " + e.getMessage());
        }
    }

    /**
     * 重新加载密码检查缓存
     */
    @PreAuthorize("@ss.hasPermi('system:password:cache')")
    @Log(title = "密码缓存管理", businessType = BusinessType.OTHER)
    @PostMapping("/reload")
    public AjaxResult reloadCache() {
        try {
            // 先清除缓存，再重新预热
            passwordCheckUtils.clearPasswordCache();
            passwordCheckUtils.warmUpCache();
            return success("密码检查缓存重新加载成功");
        } catch (Exception e) {
            return error("密码检查缓存重新加载失败: " + e.getMessage());
        }
    }
} 