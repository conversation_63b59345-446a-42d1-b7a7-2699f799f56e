package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.password.PasswordCheckPerformanceTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 密码测试控制器
 */
@RestController
@RequestMapping("/system/password-test")
public class PasswordTestController extends BaseController {

    @Autowired
    private PasswordCheckPerformanceTest performanceTest;

    /**
     * 执行性能测试
     */
    @PreAuthorize("@ss.hasPermi('system:password:test')")
    @Log(title = "密码性能测试", businessType = BusinessType.OTHER)
    @PostMapping("/performance")
    public AjaxResult testPerformance() {
        try {
            performanceTest.testPerformance();
            return success("性能测试执行完成，请查看控制台输出");
        } catch (Exception e) {
            return error("性能测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试单个密码
     */
    @PreAuthorize("@ss.hasPermi('system:password:test')")
    @Log(title = "密码测试", businessType = BusinessType.OTHER)
    @PostMapping("/single")
    public AjaxResult testSinglePassword(@RequestParam String password) {
        try {
            performanceTest.testSinglePassword(password);
            return success("单个密码测试执行完成，请查看控制台输出");
        } catch (Exception e) {
            return error("单个密码测试执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试缓存管理功能
     */
    @PreAuthorize("@ss.hasPermi('system:password:test')")
    @Log(title = "密码缓存测试", businessType = BusinessType.OTHER)
    @PostMapping("/cache")
    public AjaxResult testCacheManagement() {
        try {
            performanceTest.testCacheManagement();
            return success("缓存管理测试执行完成，请查看控制台输出");
        } catch (Exception e) {
            return error("缓存管理测试执行失败: " + e.getMessage());
        }
    }
} 