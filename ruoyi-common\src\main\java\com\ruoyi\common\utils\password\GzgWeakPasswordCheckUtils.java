package com.ruoyi.common.utils.password;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.apache.commons.io.IOUtils;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
public class GzgWeakPasswordCheckUtils {

    @Autowired
    private RedisCache redisCache;

    // Redis缓存键常量
    private static final String CACHE_KEY_FORBIDDEN_WORDS = "weak_password:forbidden_words";
    private static final String CACHE_KEY_DATES = "weak_password:dates";
    private static final String CACHE_KEY_BANNED_WORDS = "weak_password:banned_words";
    private static final String CACHE_KEY_KEYBOARD_PATTERNS = "weak_password:keyboard_patterns";

    // 缓存过期时间（24小时）
    private static final long CACHE_EXPIRE_HOURS = 24;

    // 分片缓存大小（每个分片最多包含的单词数量）
    private static final int SHARD_SIZE = 10000;

    // 线程池大小
    private static final int THREAD_POOL_SIZE = Runtime.getRuntime().availableProcessors();
    private final ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
    
    // 超时时间配置（秒）
    private static final int TIMEOUT_SECONDS = 1000;
    
    // 递归深度限制
    private static final int MAX_RECURSION_DEPTH = 20;

    // 定义替换映射：字符到替换字符串
    private static final Map<Character, String> REPLACEMENT_MAP = new HashMap<>();
    static {
        REPLACEMENT_MAP.put('a', "@");
        REPLACEMENT_MAP.put('b', "13");
        REPLACEMENT_MAP.put('o', "0");
        REPLACEMENT_MAP.put('s', "5");
        REPLACEMENT_MAP.put('z', "2");
        REPLACEMENT_MAP.put('l', "1");
        REPLACEMENT_MAP.put('e', "3");
        REPLACEMENT_MAP.put('i', "1");
    }

    // 禁止的单词数组
    private static final String[] BANNED_WORDS = {
            "cloud", "CLoUD", "password", "PASSWORD", "p@ssword", "gzport" , "com" , "gzp",
            "!qaz2wsx", "1.11111E+11", "1.12233E+11", "1.23123E+11", "1.23321E+11", "1.23456E+11", "1.23457E+11", "1.23457E+17",
            "1.23457E+19", "18atcskD2W", "1Fr2rfq7xL", "1qaz!QAZ", "1qaz!qaz", "1qaz@wsx", "1v7Upjw3nT", "1zn6FpN01x",
            "3Odi15ngxB", "3d8Cubaj2E", "3rJs1la2qE", "3rJs1la7qE", "3rJs5la8qE", "5X1CJdsb9p", "5hsU75kpoT", "5plK4L5Uc7",
            "6V21wbgad", "7uGd5HIp2J", "8PHroWZ624", "8ix6S1fceH", "AKAX89Wn", "Aa123456", "Abcd1234", "Blink123",
            "CM6E7Aumn9", "Charlie1", "D1lakiss", "Eh1K9oh335", "Groupd2013", "H2vWDuBjX4", "J1V1fp2BXm", "Letmein1",
            "Linkedin1", "MaprCheM56458", "Megaparol12345", "Michael1", "OcPOOok325", "P3Rat54797", "P@ssw0rd", "PE#5GZ29PTZMSE",
            "Pa55word", "Parola12", "Passw0rd", "Password01", "Password1", "Password123", "PolniyPizdec0211", "PolniyPizdec1102",
            "PolniyPizdec110211", "Qwerty123", "SZ9kQcCTwY", "Sample123", "Sojdlg123aljg", "Tnk0Mk16VX", "W1aUbvOQ", "W5tXn36alfW",
            "W5tn36alfW", "Welcome1", "X3LUym2MMJ", "XBLhInTB9w", "YAgjecc826", "YfDbUfNjH10305070", "a838hfiD", "asdfghjkl;&#39;",
            "c43qpul5RZ", "chivas#1", "couponSC10", "d71lWz9zjS", "d9Zufqd92N", "dIWtgm8492", "dfg5Fhg5VGFh1", "fxzZ75yer",
            "g9l2d1fzPY", "hgrFQg4577", "iG4abOX4", "iloveyou<3", "iw14Fi9j", "iw14Fi9jwQa", "iw14Fi9jxL", "j38ifUbn",
            "jG3h4HFn", "john!20130605at1753", "ka_dJKHJsy6", "mV46VkMz10", "owt243yGbJ", "p@ssw0rd", "pk3x7w9W", "qdujvyG5sxa",
            "qti7Zxh18U", "rbOTmvZ954", "s8YLPe9jDPvYM", "scvMOFAS79", "uQA9Ebw445", "vRbGQnS997", "w66YRyBgRa", "wsbe279qSG",
            "x4ivygA51F", "y6p67FtrqJ"
    };

    // 键盘模式数组
    private static final String[] KEYBOARD_PATTERNS = {
            "abc", "cba", "qaz", "qwe", "abc123", "a1b2c3",
            "147", "369", "258", "741", "852", "963", "123", "456", "789",
            "321", "654", "987", "wer", "ert", "rty", "tyu", "yui", "uio", "iop", "op[", "p[]", "[]\\",
            "asd", "sdf", "dfg", "fgh", "ghj", "hjk", "jkl", "kl;", "l;'",
            "zxc", "xcv", "cvb", "vbn", "bnm", "nm,", "m,.", ",./",
            "qaz", "wsx", "edc", "rfv", "tgb", "yhn", "ujm", "ik,", "ol.", "p;/",
            "ewq", "rew", "tre", "ytr", "uyt", "iuy", "oiu", "poi", "[po", "][p", "\\][",
            "';l", ";lk", "lkj", "kjh", "jhg", "hgf", "gfd", "fds", "dsa",
            "/.,", ".,m", ",mn", "mnb", "nbv", "bvc", "vcx", "cxz",
            "`12", "234", "345", "567", "678", "890", "90-", "0-=",
            "=-0", "-09", "098", "876", "765", "543", "432", "21`",
            "~!@", "!@#", "@#$", "#$%", "$%^", "%^&", "^&*", "&*(", "*()", "()_", ")_+",
            "+_)", "_)(", ")(*", "(*&", "*&^", "&^%", "^%$", "%$#", "#$@", "@!~"
    };

    // 缓存排序后的禁止单词列表，避免重复排序
    private volatile List<String> cachedSortedForbiddenWords = null;
    private final Object sortLock = new Object();

    // 缓存排序后的日期列表，避免重复排序
    private volatile List<String> cachedSortedDates = null;
    private final Object dateSortLock = new Object();

    /**
     * 获取排序后的禁止单词列表（带缓存）
     */
    private List<String> getSortedForbiddenWords() {
        if (cachedSortedForbiddenWords == null) {
            synchronized (sortLock) {
                if (cachedSortedForbiddenWords == null) {
                    try {
                        Set<String> baseForbiddenWords = getOrCreateForbiddenWords();
                        if (baseForbiddenWords == null || baseForbiddenWords.isEmpty()) {
                            System.err.println("警告: 禁止单词集合为空或null");
                            return new ArrayList<>();
                        }
                        cachedSortedForbiddenWords = baseForbiddenWords.stream()
                                .sorted((a, b) -> Integer.compare(a.length(), b.length()))
                                .collect(Collectors.toList());
                    } catch (Exception e) {
                        System.err.println("获取排序禁止单词异常: " + e.getMessage());
                        System.err.println("异常堆栈: ");
                        e.printStackTrace();
                        return new ArrayList<>();
                    }
                }
            }
        }
        return cachedSortedForbiddenWords;
    }

    /**
     * 获取排序后的日期列表（带缓存）
     */
    private List<String> getSortedDates() {
        if (cachedSortedDates == null) {
            synchronized (dateSortLock) {
                if (cachedSortedDates == null) {
                    try {
                        List<String> allDates = getOrCreateDateList();
                        if (allDates == null || allDates.isEmpty()) {
                            System.err.println("警告: 日期列表为空或null");
                            return new ArrayList<>();
                        }
                        cachedSortedDates = allDates.stream()
                                .sorted((a, b) -> Integer.compare(a.length(), b.length()))
                                .collect(Collectors.toList());
                    } catch (Exception e) {
                        System.err.println("获取排序日期异常: " + e.getMessage());
                        System.err.println("异常堆栈: ");
                        e.printStackTrace();
                        return new ArrayList<>();
                    }
                }
            }
        }
        return cachedSortedDates;
    }

    /**
     * 生成输入字符串的所有可能替换组合。
     *
     * @param input 输入字符串
     * @return 所有可能替换后的字符串列表
     */
    public static List<String> generateReplacements(String input) {
        List<String> result = new ArrayList<>();
        if (input == null || input.isEmpty()) {
            result.add(""); // 空字符串输入时返回空字符串
            return result;
        }
        generateHelper(input, 0, "", result);
        return result;
    }

    /**
     * 递归辅助函数，构建所有替换组合。
     *
     * @param input  原始输入字符串
     * @param index  当前处理的字符索引
     * @param current 当前构建的字符串
     * @param result 存储所有结果的列表
     */
    private static void generateHelper(String input, int index, String current, List<String> result) {
        // 当索引达到字符串长度时，添加当前构建的字符串到结果
        if (index == input.length()) {
            result.add(current);
            return;
        }

        char c = input.charAt(index);
        // 检查当前字符是否可替换
        if (REPLACEMENT_MAP.containsKey(c)) {
            // 分支1：不替换，添加原字符
            generateHelper(input, index + 1, current + c, result);
            // 分支2：替换，添加替换字符串
            String replacement = REPLACEMENT_MAP.get(c);
            generateHelper(input, index + 1, current + replacement, result);
        } else {
            // 不可替换字符，直接添加
            generateHelper(input, index + 1, current + c, result);
        }
    }

    /**
     * 获取或初始化禁止单词集合（带缓存）
     */
    private Set<String> getOrCreateForbiddenWords() {
        // 尝试从缓存获取
        Set<String> forbiddenWords = getForbiddenWordsFromCache();
        if (forbiddenWords != null && !forbiddenWords.isEmpty()) {
            return forbiddenWords;
        }

        // 缓存中没有，重新构建
        synchronized (this) {
            // 双重检查锁定
            forbiddenWords = getForbiddenWordsFromCache();
            if (forbiddenWords != null && !forbiddenWords.isEmpty()) {
                return forbiddenWords;
            }

            try {
                forbiddenWords = buildForbiddenWords();
                // 分片缓存禁止单词集合
                cacheForbiddenWordsInShards(forbiddenWords);
                return forbiddenWords;
            } catch (IOException e) {
                throw new RuntimeException("初始化禁止单词字典/拼音失败", e);
            }
        }
    }

    /**
     * 从分片缓存中获取禁止单词集合
     */
    private Set<String> getForbiddenWordsFromCache() {
        Set<String> allWords = new HashSet<>();
        int shardIndex = 0;

        while (true) {
            String shardKey = CACHE_KEY_FORBIDDEN_WORDS + ":shard:" + shardIndex;
            Set<String> shard = redisCache.getCacheObject(shardKey);
            if (shard == null || shard.isEmpty()) {
                break;
            }
            allWords.addAll(shard);
            shardIndex++;
        }

        return allWords.isEmpty() ? null : allWords;
    }

    /**
     * 将禁止单词集合分片缓存
     */
    private void cacheForbiddenWordsInShards(Set<String> forbiddenWords) {
        List<String> wordList = new ArrayList<>(forbiddenWords);
        int shardIndex = 0;

        for (int i = 0; i < wordList.size(); i += SHARD_SIZE) {
            int endIndex = Math.min(i + SHARD_SIZE, wordList.size());
            Set<String> shard = new HashSet<>(wordList.subList(i, endIndex));

            String shardKey = CACHE_KEY_FORBIDDEN_WORDS + ":shard:" + shardIndex;
            redisCache.setCacheObject(shardKey, shard, (int)CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            shardIndex++;
        }
    }

    /**
     * 构建禁止单词集合
     */
    private static Set<String> buildForbiddenWords() throws IOException {
        Set<String> forbiddenWords = new HashSet<>();

        // 读取英文词典
        String content = IOUtils.toString(new ClassPathResource("words_dictionary.json")
                .getInputStream(), StandardCharsets.UTF_8);
        JSONObject jsonObject = JSONObject.parseObject(content);

        for (String word : jsonObject.keySet()) {
            int len = word.length();
            if (len >= 3 && len <= 20) {
                String lowerWord = word.toLowerCase();
                forbiddenWords.add(lowerWord);
                // 移除预生成替换组合，改为运行时生成
//                List<String> replacements = generateReplacements(lowerWord);
//                forbiddenWords.addAll(replacements);
            }
        }

        // 读取拼音词典
        String pyContent = IOUtils.toString(new ClassPathResource("pinyin_dictionary.json")
                .getInputStream(), StandardCharsets.UTF_8);
        List<String> jsonArray = JSONArray.parseArray(pyContent, String.class);

        for (String word : jsonArray) {
            int len = word.length();
            if (len >= 3) {
                String lowerWord = word.toLowerCase();
                forbiddenWords.add(lowerWord);
                // 移除预生成替换组合，改为运行时生成
//                List<String> replacements = generateReplacements(lowerWord);
//                forbiddenWords.addAll(replacements);
            }
        }

        return forbiddenWords;
    }

    /**
     * 获取或初始化日期列表（带缓存）
     */
    private List<String> getOrCreateDateList() {
        // 尝试从缓存获取
        List<String> dateList = redisCache.getCacheObject(CACHE_KEY_DATES);
        if (dateList != null) {
            return dateList;
        }

        // 缓存中没有，重新构建
        synchronized (this) {
            // 双重检查锁定
            dateList = redisCache.getCacheObject(CACHE_KEY_DATES);
            if (dateList != null) {
                return dateList;
            }

            dateList = buildDateList();
            // 缓存日期列表
            redisCache.setCacheObject(CACHE_KEY_DATES, dateList, (int)CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            return dateList;
        }
    }

    /**
     * 构建日期列表
     */
    private static List<String> buildDateList() {
        List<String> allDates = new ArrayList<>();

        // 优化：减少日期范围从1900-2099到1950-2030，大幅减少日期数量
        for (int year = 1950; year <= 2030; year++) {
            for (int month = 1; month <= 12; month++) {
                int daysInMonth = getDaysInMonth(year, month);

                for (int day = 1; day <= daysInMonth; day++) {
                    // 格式化为YYYY-MM-DD并添加到列表
                    String dateStr = String.format("%04d-%02d-%02d", year, month, day);

                    Date date = DateUtils.parseDate(dateStr);
                    allDates.add(DateUtils.parseDateToStr("yyyyMMdd", date));
                }
            }
        }

        return allDates;
    }

    /**
     * 验证密码（高性能优化版本）
     */
    /**
     * 并行检查原始密码中的禁止单词
     */
    private CompletableFuture<String> findFirstMatchAsync(String input, Set<String> forbiddenWords) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<String> sortedWords = getSortedForbiddenWords();
                for (String word : sortedWords) {
                    if (input.contains(word)) {
                        return word;
                    }
                }
                return null;
            } catch (Exception e) {
                System.err.println("原始密码检查异常: " + e.getMessage());
                System.err.println("异常堆栈: ");
                e.printStackTrace();
                return null;
            }
        }, executorService);
    }

    /**
     * 并行检查替换组合中的禁止单词
     */
    private CompletableFuture<String> findFirstMatchInReplacementsAsync(String input, Set<String> forbiddenWords) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return findFirstMatchInReplacementsOptimized(input, forbiddenWords);
            } catch (Exception e) {
                System.err.println("替换组合检查异常: " + e.getMessage());
                System.err.println("异常堆栈: ");
                e.printStackTrace();
                return null;
            }
        }, executorService);
    }

    public List<String> validatePassword(String password) {
        List<String> reasons = new ArrayList<>();
        if (password == null || password.isEmpty()) return reasons;

        String lowerPwd = password.toLowerCase();

        // 获取基础禁止单词集合（不包含替换组合）
        Set<String> baseForbiddenWords = getOrCreateForbiddenWords();

        // 并行执行原始密码检查和替换组合检查
        CompletableFuture<String> originalMatchFuture = findFirstMatchAsync(lowerPwd, baseForbiddenWords);
        CompletableFuture<String> replacementMatchFuture = findFirstMatchInReplacementsAsync(lowerPwd, baseForbiddenWords);

        try {
            // 等待原始密码检查结果（优先检查）
            String firstMatchedWord = originalMatchFuture.get(3, TimeUnit.SECONDS);
            
            // 如果原始密码中没有找到，等待替换组合检查结果
            if (firstMatchedWord == null) {
                firstMatchedWord = replacementMatchFuture.get(3, TimeUnit.SECONDS);
            }

            if (firstMatchedWord != null) {
                reasons.add("密码包含禁止的单词: " + firstMatchedWord);
            }

        } catch (Exception e) {
            // 如果并行处理异常，回退到串行处理
            System.err.println("并行密码检查异常，回退到串行处理: " + e.getMessage());
            String firstMatchedWord = findFirstMatch(lowerPwd, baseForbiddenWords);
            if (firstMatchedWord == null) {
                firstMatchedWord = findFirstMatchInReplacements(lowerPwd, baseForbiddenWords);
            }
            if (firstMatchedWord != null) {
                reasons.add("密码包含禁止的单词: " + firstMatchedWord);
            }
        }

        return reasons;
    }

    /**
     * 在原始字符串中查找第一个匹配的禁止单词（优化版本）
     */
    private String findFirstMatch(String input, Set<String> forbiddenWords) {
        // 优化3: 使用缓存的排序列表，优先检查短单词（更可能匹配）
        List<String> sortedWords = getSortedForbiddenWords();

        for (String word : sortedWords) {
            if (input.contains(word)) {
                return word;
            }
        }
        return null;
    }

    /**
     * 在替换组合中查找第一个匹配的禁止单词（优化版本）
     */
    private String findFirstMatchInReplacements(String input, Set<String> forbiddenWords) {
        // 优化4: 只生成必要的替换组合，找到第一个匹配就停止
        return findFirstMatchInReplacementsOptimized(input, forbiddenWords);
    }

    /**
     * 优化的替换组合检查（避免生成所有组合）
     */
    private String findFirstMatchInReplacementsOptimized(String input, Set<String> forbiddenWords) {
        // 优化5: 使用递归但带早期终止的检查
        return checkReplacementsRecursively(input, 0, "", forbiddenWords);
    }

    /**
     * 串行版本的密码验证（避免嵌套并行处理）
     */
    private List<String> validatePasswordSerial(String password) {
        List<String> reasons = new ArrayList<>();
        if (password == null || password.isEmpty()) return reasons;

        System.err.println("开始串行密码验证，密码长度: " + password.length());
        long startTime = System.currentTimeMillis();

        String lowerPwd = password.toLowerCase();

        // 获取基础禁止单词集合（不包含替换组合）
        System.err.println("获取禁止单词集合...");
        Set<String> baseForbiddenWords = getOrCreateForbiddenWords();
        System.err.println("禁止单词集合大小: " + (baseForbiddenWords != null ? baseForbiddenWords.size() : "null"));

        // 串行执行原始密码检查和替换组合检查
        System.err.println("开始原始密码检查...");
        String firstMatchedWord = findFirstMatch(lowerPwd, baseForbiddenWords);
        if (firstMatchedWord == null) {
            System.err.println("原始密码检查无匹配，开始替换组合检查...");
            System.err.println("密码包含可替换字符数量: " + countReplaceableChars(lowerPwd));
            System.err.println("替换组合检查将限制字符串长度为20位");
            firstMatchedWord = findFirstMatchInReplacements(lowerPwd, baseForbiddenWords);
        }

        if (firstMatchedWord != null) {
            reasons.add("密码包含禁止的单词: " + firstMatchedWord);
        }

        long endTime = System.currentTimeMillis();
        System.err.println("串行密码验证完成，耗时: " + (endTime - startTime) + "ms");

        return reasons;
    }
    
    /**
     * 统计密码中可替换字符的数量
     */
    private int countReplaceableChars(String password) {
        int count = 0;
        for (char c : password.toCharArray()) {
            if (REPLACEMENT_MAP.containsKey(c)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 串行版本检查禁止单词
     */
    private String checkBannedWordsSerial(String password) {
        try {
            for (String bannedWord : BANNED_WORDS) {
                if (password.equalsIgnoreCase(bannedWord) || password.toLowerCase().contains(bannedWord.toLowerCase())) {
                    return "密码包含禁止的单词：" + bannedWord;
                }
            }
            return null;
        } catch (Exception e) {
            System.err.println("串行禁止单词检查异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 串行版本检查键盘模式
     */
    private String checkKeyboardPatternsSerial(String password) {
        try {
            for (String pattern : KEYBOARD_PATTERNS) {
                if (password.equalsIgnoreCase(pattern) || password.matches(".*" + Pattern.quote(pattern) + ".*")) {
                    return "密码包含连续键位字母序列：" + pattern;
                }
            }
            return null;
        } catch (Exception e) {
            System.err.println("串行键盘模式检查异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 串行版本检查日期模式
     */
    private String checkDatePatternsSerial(String password) {
        try {
            List<String> allDates = getSortedDates();
            for (String dateStr : allDates) {
                if(password.contains(dateStr) || password.matches(".*" + Pattern.quote(dateStr) + ".*") || containsLongConsecutiveSubstring(dateStr,password,4)){
                    return "密码包含年份/日期中连续的部分内容";
                }
            }
            return null;
        } catch (Exception e) {
            System.err.println("串行日期模式检查异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 串行版本检查用户名和手机号
     */
    private String checkUserInfoSerial(String password, String username, String phone) {
        try {
            if (password.contains(username) || password.matches(".*" + Pattern.quote(username) + ".*") || containsLongConsecutiveSubstring(username,password,3)) {
                return "密码包含用户名或用户名中连续的部分内容";
            }
            
            if(StringUtils.isNotEmpty(phone)){
                if(password.contains(phone) || password.matches(".*" + Pattern.quote(phone) + ".*") || containsLongConsecutiveSubstring(phone,password,4)){
                    return "密码包含手机号或手机号中连续的部分内容";
                }
            }
            return null;
        } catch (Exception e) {
            System.err.println("串行用户信息检查异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 串行版本检查默认密码
     */
    private String checkDefaultPasswordSerial(String password) {
        try {
            String originPassword = "G2p4rt&Sh1p";
            if(password.equals(originPassword)){
                return "密码与系统默认密码相同";
            }
            return null;
        } catch (Exception e) {
            System.err.println("串行默认密码检查异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 优化的递归替换检查（带深度限制和早期终止）
     */
    private String checkReplacementsRecursively(String input, int index, String current, Set<String> forbiddenWords) {
        // 如果已经处理完所有字符
        if (index == input.length()) {
            // 检查当前组合是否包含禁止单词
            for (String word : forbiddenWords) {
                if (current.contains(word)) {
                    return word;
                }
            }
            return null;
        }
        
        // 深度限制检查
        if (index >= MAX_RECURSION_DEPTH) {
            // 如果达到深度限制，只检查当前已构建的部分
            for (String word : forbiddenWords) {
                if (current.contains(word)) {
                    return word;
                }
            }
            return null;
        }

        char c = input.charAt(index);
        
        // 检查当前字符是否可替换
        if (REPLACEMENT_MAP.containsKey(c)) {
            // 分支1：不替换，添加原字符
            String newCurrent1 = current + c;
            // 如果长度超过20，截取前20位
            if (newCurrent1.length() > 20) {
                newCurrent1 = newCurrent1.substring(0, 20);
            }
            String result = checkReplacementsRecursively(input, index + 1, newCurrent1, forbiddenWords);
            if (result != null) {
                return result;
            }
            
            // 分支2：替换，添加替换字符串
            String replacement = REPLACEMENT_MAP.get(c);
            String newCurrent2 = current + replacement;
            // 如果长度超过20，截取前20位
            if (newCurrent2.length() > 20) {
                newCurrent2 = newCurrent2.substring(0, 20);
            }
            result = checkReplacementsRecursively(input, index + 1, newCurrent2, forbiddenWords);
            if (result != null) {
                return result;
            }
        } else {
            // 不可替换字符，直接添加
            String newCurrent = current + c;
            // 如果长度超过20，截取前20位
            if (newCurrent.length() > 20) {
                newCurrent = newCurrent.substring(0, 20);
            }
            String result = checkReplacementsRecursively(input, index + 1, newCurrent, forbiddenWords);
            if (result != null) {
                return result;
            }
        }
        
        return null;
    }

    /**
     * 并行检查禁止单词
     */
    private CompletableFuture<String> checkBannedWordsAsync(String password) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                for (String bannedWord : BANNED_WORDS) {
                    if (password.equalsIgnoreCase(bannedWord) || password.toLowerCase().contains(bannedWord.toLowerCase())) {
                        return "密码包含禁止的单词：" + bannedWord;
                    }
                }
                return null;
            } catch (Exception e) {
                System.err.println("禁止单词检查异常: " + e.getMessage());
                System.err.println("异常堆栈: ");
                e.printStackTrace();
                return null;
            }
        }, executorService);
    }

    /**
     * 并行检查键盘模式
     */
    private CompletableFuture<String> checkKeyboardPatternsAsync(String password) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                for (String pattern : KEYBOARD_PATTERNS) {
                    if (password.equalsIgnoreCase(pattern) || password.matches(".*" + Pattern.quote(pattern) + ".*")) {
                        return "密码包含连续键位字母序列：" + pattern;
                    }
                }
                return null;
            } catch (Exception e) {
                System.err.println("键盘模式检查异常: " + e.getMessage());
                System.err.println("异常堆栈: ");
                e.printStackTrace();
                return null;
            }
        }, executorService);
    }

    /**
     * 并行检查日期模式
     */
    private CompletableFuture<String> checkDatePatternsAsync(String password) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<String> allDates = getSortedDates();
                for (String dateStr : allDates) {
                    if(password.contains(dateStr) || password.matches(".*" + Pattern.quote(dateStr) + ".*") || containsLongConsecutiveSubstring(dateStr,password,4)){
                        return "密码包含年份/日期中连续的部分内容";
                    }
                }
                return null;
            } catch (Exception e) {
                System.err.println("日期模式检查异常: " + e.getMessage());
                System.err.println("异常堆栈: ");
                e.printStackTrace();
                return null;
            }
        }, executorService);
    }

    /**
     * 并行检查用户名和手机号
     */
    private CompletableFuture<String> checkUserInfoAsync(String password, String username, String phone) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (password.contains(username) || password.matches(".*" + Pattern.quote(username) + ".*") || containsLongConsecutiveSubstring(username,password,3)) {
                    return "密码包含用户名或用户名中连续的部分内容";
                }
                
                if(StringUtils.isNotEmpty(phone)){
                    if(password.contains(phone) || password.matches(".*" + Pattern.quote(phone) + ".*") || containsLongConsecutiveSubstring(phone,password,4)){
                        return "密码包含手机号或手机号中连续的部分内容";
                    }
                }
                return null;
            } catch (Exception e) {
                System.err.println("用户信息检查异常: " + e.getMessage());
                System.err.println("异常堆栈: ");
                e.printStackTrace();
                return null;
            }
        }, executorService);
    }

    /**
     * 并行检查默认密码
     */
    private CompletableFuture<String> checkDefaultPasswordAsync(String password) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String originPassword = "G2p4rt&Sh1p";
                if(password.equals(originPassword)){
                    return "密码与系统默认密码相同";
                }
                return null;
            } catch (Exception e) {
                System.err.println("默认密码检查异常: " + e.getMessage());
                System.err.println("异常堆栈: ");
                e.printStackTrace();
                return null;
            }
        }, executorService);
    }

    /**
     * 并行检查密码字典（优化版本）
     */
    private CompletableFuture<List<String>> checkPasswordDictionaryAsync(String password) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 直接调用串行版本的检查，避免嵌套并行处理
                return validatePasswordSerial(password);
            } catch (Exception e) {
                System.err.println("密码字典检查异常: " + e.getMessage());
                System.err.println("异常堆栈: ");
                e.printStackTrace();
                return new ArrayList<>();
            }
        }, executorService);
    }

    /**
     * 获取密码检查结果（多线程并行优化版本）
     */
    public String getGzgWeakPasswordReasons(String password, String username, String phone) throws IOException {
        List<String> reasons = new ArrayList<>();
        
        // 基础检查（快速检查，不需要并行）
        if (password.length() < 8 || password.length() > 20) {
            reasons.add("密码长度必须在8到20个字符之间");
        }

        boolean hasDigit = password.matches(".*\\d.*");
        boolean hasLowercase = password.matches(".*[a-z].*");
        boolean hasUppercase = password.matches(".*[A-Z].*");
        boolean hasSpecialChar = password.matches(".*[!@#$%^&*+=:;<>,.?/].*");
        int count = (hasDigit ? 1 : 0) + (hasLowercase ? 1 : 0) + (hasUppercase ? 1 : 0) + (hasSpecialChar ? 1 : 0);
        if (count < 3) {
            reasons.add("密码至少需要包含数字、小写字母、大写字母、特殊字符中的三项");
        }

        if(!"ok".equals(checkRepeat(password))){
            reasons.add("不能连续3字母或数字");
        }

        if(!"ok".equals(check3(password))){
            reasons.add("包含三个或者三个以上相同");
        }

        if(!"ok".equals(validateKey(password))) {
            reasons.add("不得包含键盘上任意连续三个或者三个以上字符");
        }

        // 并行执行所有检查任务
        CompletableFuture<String> bannedWordsFuture = checkBannedWordsAsync(password);
        CompletableFuture<String> keyboardPatternsFuture = checkKeyboardPatternsAsync(password);
        CompletableFuture<String> datePatternsFuture = checkDatePatternsAsync(password);
        CompletableFuture<String> userInfoFuture = checkUserInfoAsync(password, username, phone);
        CompletableFuture<String> defaultPasswordFuture = checkDefaultPasswordAsync(password);
        CompletableFuture<List<String>> passwordDictionaryFuture = checkPasswordDictionaryAsync(password);

        // 等待所有任务完成并收集结果
        try {
            System.err.println("开始并行密码检查，超时时间: " + TIMEOUT_SECONDS + "秒");
            
            // 使用超时机制，避免长时间等待
            String bannedWordResult = bannedWordsFuture.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (bannedWordResult != null) {
                reasons.add(bannedWordResult);
            }

            String keyboardPatternResult = keyboardPatternsFuture.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (keyboardPatternResult != null) {
                reasons.add(keyboardPatternResult);
            }

            String datePatternResult = datePatternsFuture.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (datePatternResult != null) {
                reasons.add(datePatternResult);
            }

            String userInfoResult = userInfoFuture.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (userInfoResult != null) {
                reasons.add(userInfoResult);
            }

            String defaultPasswordResult = defaultPasswordFuture.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (defaultPasswordResult != null) {
                reasons.add(defaultPasswordResult);
            }

            System.err.println("开始检查密码字典（最耗时的步骤）...");
            List<String> passwordDictionaryResult = passwordDictionaryFuture.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (passwordDictionaryResult != null && !passwordDictionaryResult.isEmpty()) {
                reasons.addAll(passwordDictionaryResult);
            }
            System.err.println("密码字典检查完成");

        } catch (Exception e) {
            // 如果某个任务超时或异常，记录详细日志并回退到串行处理
            System.err.println("密码检查并行处理异常: " + e.getMessage());
            System.err.println("异常堆栈: ");
            e.printStackTrace();
            
            // 回退到串行处理
            System.err.println("回退到串行处理...");
            try {
                // 串行执行各项检查
                String bannedWordResult = checkBannedWordsSerial(password);
                if (bannedWordResult != null) {
                    reasons.add(bannedWordResult);
                }

                String keyboardPatternResult = checkKeyboardPatternsSerial(password);
                if (keyboardPatternResult != null) {
                    reasons.add(keyboardPatternResult);
                }

                String datePatternResult = checkDatePatternsSerial(password);
                if (datePatternResult != null) {
                    reasons.add(datePatternResult);
                }

                String userInfoResult = checkUserInfoSerial(password, username, phone);
                if (userInfoResult != null) {
                    reasons.add(userInfoResult);
                }

                String defaultPasswordResult = checkDefaultPasswordSerial(password);
                if (defaultPasswordResult != null) {
                    reasons.add(defaultPasswordResult);
                }

                List<String> passwordDictionaryResult = validatePasswordSerial(password);
                if (passwordDictionaryResult != null && !passwordDictionaryResult.isEmpty()) {
                    reasons.addAll(passwordDictionaryResult);
                }
            } catch (Exception serialException) {
                System.err.println("串行处理也发生异常: " + serialException.getMessage());
                serialException.printStackTrace();
            }
        }

        reasons = reasons.stream().distinct().collect(Collectors.toList());

        if (reasons.isEmpty()) {
            return "密码强度足够";
        } else {
            return String.join("; ", reasons);
        }
    }

    /**
     * 清除所有密码相关的缓存
     */
    public void clearPasswordCache() {
        // 清除分片缓存
        int shardIndex = 0;
        while (true) {
            String shardKey = CACHE_KEY_FORBIDDEN_WORDS + ":shard:" + shardIndex;
            Set<String> shard = redisCache.getCacheObject(shardKey);
            if (shard == null || shard.isEmpty()) {
                break;
            }
            redisCache.deleteObject(shardKey);
            shardIndex++;
        }

        // 清除其他缓存
        redisCache.deleteObject(CACHE_KEY_FORBIDDEN_WORDS);
        redisCache.deleteObject(CACHE_KEY_DATES);
        redisCache.deleteObject(CACHE_KEY_BANNED_WORDS);
        redisCache.deleteObject(CACHE_KEY_KEYBOARD_PATTERNS);
        
        // 清除排序缓存
        synchronized (sortLock) {
            cachedSortedForbiddenWords = null;
        }
        
        // 清除日期排序缓存
        synchronized (dateSortLock) {
            cachedSortedDates = null;
        }
    }

    /**
     * 预热缓存（在应用启动时调用）
     */
    public void warmUpCache() {
        try {
            System.out.println("开始预热密码检查缓存...");

            // 预加载日期列表（数据量较小）
            getOrCreateDateList();
            System.out.println("日期列表缓存预热完成");

            // 预加载禁止单词集合（数据量大，可能耗时较长）
            System.out.println("开始加载禁止单词集合...");
            getOrCreateForbiddenWords();
            System.out.println("禁止单词集合缓存预热完成");

            System.out.println("密码检查缓存预热完成");
        } catch (Exception e) {
            // 记录日志但不抛出异常，避免影响应用启动
            System.err.println("密码缓存预热失败: " + e.getMessage());
            System.err.println("应用将继续启动，但首次密码检查可能较慢");
        }
    }

    /**
     * 预热缓存（轻量级版本，只预热必要的数据）
     */
    public void warmUpCacheLight() {
        try {
            System.out.println("开始轻量级密码检查缓存预热...");

            // 只预热日期列表，禁止单词集合在需要时再加载
            getOrCreateDateList();
            System.out.println("轻量级密码检查缓存预热完成");
        } catch (Exception e) {
            System.err.println("轻量级密码缓存预热失败: " + e.getMessage());
        }
    }

    /**
     * 转码
     *
     * @param c 字符
     * @return 编码
     */
    private static int getUnicode(char c) {
        String returnUniCode = null;
        returnUniCode = String.valueOf((int) c);
        return Integer.parseInt(returnUniCode);
    }

    /***
     * 描述: 三个或者三个以上相同
     *
     * @param pwd 密码
     * @return String
     */
    public static String check3(String pwd) {

        String regx = "^.*(.)\\1{2}.*$";
        Matcher m = null;
        Pattern p = null;
        p = Pattern.compile(regx);
        m = p.matcher(pwd);
        if (m.matches()) {
            return "包含三个或者三个以上相同";
        } else {
            return "ok";

        }

    }

    /***
     * 描述: 密码不得包含键盘上任意连续的三个字符或shift转换字符
     *
     * @param str 字符串
     * @return String
     */
    public static String validateKey(String str) {

        //定义横向穷举
        String[][] keyCode = {
                {"`~·", "1=", "2@@", "3#", "4$￥", "5%", "6^……", "7&", "8*", "9(（", "0）)", "-_", "=+"},
                {" ", "qQ", "wW", "eE", "rR", "tT", "yY", "uU", "iI", "oO", "pP", "[{【", "]}】", "\\|、"},
                {" ", "aA", "sS", "dD", "fF", "gG", "hH", "jJ", "kK", "lL", ";:", "\'\"’“"},
                {" ", "zZ", "xX", "cC", "vV", "bB", "nN", "mM", ",《<", ".>》", "/?？"}
        };

        //找出给出的字符串，每个字符，在坐标系中的位置。
        char[] c = str.toCharArray();
        List<Integer> x = new ArrayList<>();
        List<Integer> y = new ArrayList<>();
        for (int i = 0; i < c.length; i++) {
            char temp = c[i];
            toHere:
            for (int j = 0; j < keyCode.length; j++) {
                for (int k = 0; k < keyCode[j].length; k++) {
                    String jk = keyCode[j][k];
                    if (jk.contains(String.valueOf(temp))) {
                        x.add(j);
                        y.add(k);
                        break toHere;
                    }
                }
            }
        }
        boolean flag = false;
        for (int i = 0; i < x.size() - 2; i++) {
            // 如果X一致，那么就是在一排
            if (x.get(i) == x.get(i + 1) && x.get(i + 1) == x.get(i + 2)) {//四者在同一行上
                if (y.get(i) > y.get(i + 2)) {
                    if (y.get(i) - 1 == y.get(i + 1) && y.get(i) - 2 == y.get(i + 2)) {
                        flag = true;
                        break;
                    }
                } else {
                    if (y.get(i) + 1 == y.get(i + 1) && y.get(i) + 2 == y.get(i + 2)) {
                        flag = true;
                        break;
                    }
                }

            } else if (x.get(i) != x.get(i + 1)
                    && x.get(i + 1) != x.get(i + 2)
                    && x.get(i) != x.get(i + 2)
            ) {//四者均不在同一行上,但是如果y相同，说明是一列
                if (y.get(i) == y.get(i + 1) && y.get(i + 1) == y.get(i + 2)) {
                    flag = true;
                    break;
                }
            }

        }
        if (flag) {
            return "不得包含键盘上任意连续三个或者三个以上字符";

        } else {
            return "ok";
        }
    }

    /***
     * 描述: 不能连续字符（如123、abc）连续3位或3位以上
     *
     * @param str 字符串
     * @return String
     */
    public static String checkRepeat(String str) {
        String[] arr = str.split("");
        boolean flag = false;
        for (int i = 1; i < arr.length - 1; i++) {
            int firstIndex = getUnicode(arr[i - 1].charAt(0));
            int secondIndex = getUnicode(arr[i].charAt(0));
            int thirdIndex = getUnicode(arr[i + 1].charAt(0));
            if ((thirdIndex - secondIndex == 1) && (secondIndex - firstIndex == 1)) {
                flag = true;
            }
        }
        if (flag) {
            return "不能连续3字母或数字";

        } else {
            return "ok";
        }
    }

    private static int getDaysInMonth(int year, int month) {
        return switch (month) {
            case 4, 6, 9, 11 -> 30;  // 4月、6月、9月、11月
            case 2 -> isLeapYear(year) ? 29 : 28;  // 2月（闰年检查）
            default -> 31;  // 其他月份（1,3,5,7,8,10,12）
        };
    }

    private static boolean isLeapYear(int year) {
        // 闰年规则：能被4整除但不能被100整除，或者能被400整除
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    public static boolean containsLongConsecutiveSubstring(String A, String B, Integer length) {
        int lengthA = A.length();

        // 遍历A的所有可能的连续子字符串（长度至少为3）
        for (int i = 0; i <= lengthA - length; i++) {
            String substring = A.substring(i, i + 3); // 获取长度为3的子字符串
            // 如果子字符串长度小于A的剩余部分，则继续增加长度并检查
            for (int j = length; j <= lengthA - i; j++) {
                substring = A.substring(i, i + j); // 更新子字符串为更长的部分
                if (B.contains(substring)) {
                    return true; // 如果B包含这个子字符串，则返回true
                }
            }
            // 注意：这里的内层循环其实是不必要的，因为一旦找到长度为3的匹配，
            // 后面的更长匹配就已经被包含了。但为了保持逻辑的完整性，我还是保留了它。
            // 在实际应用中，你可以只检查长度为3的子字符串，或者根据需要调整检查的长度。
        }

        // 如果没有找到任何匹配，则返回false
        return false;
    }

    /**
     * 关闭线程池（在应用关闭时调用）
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 调试方法：检查系统状态
     */
    public String debugSystemStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== 密码检查系统状态 ===\n");
        
        try {
            // 检查Redis连接
            status.append("Redis连接状态: ");
            try {
                redisCache.getCacheObject("test");
                status.append("正常\n");
            } catch (Exception e) {
                status.append("异常: ").append(e.getMessage()).append("\n");
            }
            
            // 检查禁止单词
            status.append("禁止单词数量: ");
            try {
                Set<String> words = getOrCreateForbiddenWords();
                status.append(words != null ? words.size() : "null").append("\n");
            } catch (Exception e) {
                status.append("获取异常: ").append(e.getMessage()).append("\n");
            }
            
            // 检查排序禁止单词
            status.append("排序禁止单词数量: ");
            try {
                List<String> sortedWords = getSortedForbiddenWords();
                status.append(sortedWords != null ? sortedWords.size() : "null").append("\n");
            } catch (Exception e) {
                status.append("获取异常: ").append(e.getMessage()).append("\n");
            }
            
            // 检查日期列表
            status.append("日期列表数量: ");
            try {
                List<String> dates = getSortedDates();
                status.append(dates != null ? dates.size() : "null").append("\n");
            } catch (Exception e) {
                status.append("获取异常: ").append(e.getMessage()).append("\n");
            }
            
            // 检查线程池状态
            status.append("线程池状态: ");
            if (executorService != null) {
                status.append("已创建, 关闭状态: ").append(executorService.isShutdown()).append("\n");
            } else {
                status.append("null\n");
            }
            
        } catch (Exception e) {
            status.append("调试过程发生异常: ").append(e.getMessage()).append("\n");
            e.printStackTrace();
        }
        
        return status.toString();
    }
}
