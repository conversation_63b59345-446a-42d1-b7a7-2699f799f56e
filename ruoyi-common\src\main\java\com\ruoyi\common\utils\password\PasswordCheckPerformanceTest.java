package com.ruoyi.common.utils.password;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 密码检查性能测试类
 * 用于测试缓存优化后的性能提升
 */
@Component
public class PasswordCheckPerformanceTest {

    @Autowired
    private GzgWeakPasswordCheckUtils passwordCheckUtils;

    /**
     * 测试密码检查性能
     */
    public void testPerformance() {
        String[] testPasswords = {
            "password123",
            "admin123",
            "qwerty123",
            "123456789",
            "abcdefgh",
            "TestPassword123!",
            "StrongP@ssw0rd",
            "Weak123",
            "Complex!@#$%^&*()",
            "Simple123"
        };

        System.out.println("=== 密码检查性能测试 ===");
        
        // 测试未缓存状态（首次调用）
        System.out.println("\n1. 首次调用（未缓存状态）:");
        long startTime = System.currentTimeMillis();
        for (String password : testPasswords) {
            try {
                passwordCheckUtils.getGzgWeakPasswordReasons(password, "testuser", "13800138000");
            } catch (IOException e) {
                System.err.println("测试失败: " + e.getMessage());
            }
        }
        long firstCallTime = System.currentTimeMillis() - startTime;
        System.out.println("首次调用总时间: " + firstCallTime + "ms");
        System.out.println("平均每次调用: " + (firstCallTime / testPasswords.length) + "ms");

        // 等待一下，确保缓存生效
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 测试缓存状态（后续调用）
        System.out.println("\n2. 后续调用（缓存状态）:");
        startTime = System.currentTimeMillis();
        for (String password : testPasswords) {
            try {
                passwordCheckUtils.getGzgWeakPasswordReasons(password, "testuser", "13800138000");
            } catch (IOException e) {
                System.err.println("测试失败: " + e.getMessage());
            }
        }
        long cachedCallTime = System.currentTimeMillis() - startTime;
        System.out.println("缓存调用总时间: " + cachedCallTime + "ms");
        System.out.println("平均每次调用: " + (cachedCallTime / testPasswords.length) + "ms");

        // 计算性能提升
        if (firstCallTime > 0) {
            double improvement = (double) firstCallTime / cachedCallTime;
            System.out.println("\n3. 性能提升:");
            System.out.println("性能提升倍数: " + String.format("%.2f", improvement) + "x");
            System.out.println("性能提升百分比: " + String.format("%.2f", (improvement - 1) * 100) + "%");
        }

        // 测试缓存预热
        System.out.println("\n4. 测试缓存预热:");
        startTime = System.currentTimeMillis();
        passwordCheckUtils.warmUpCache();
        long warmupTime = System.currentTimeMillis() - startTime;
        System.out.println("缓存预热时间: " + warmupTime + "ms");

        System.out.println("\n=== 测试完成 ===");
    }

    /**
     * 测试单个密码检查
     */
    public void testSinglePassword(String password) {
        System.out.println("测试密码: " + password);
        
        long startTime = System.nanoTime();
        try {
            String result = passwordCheckUtils.getGzgWeakPasswordReasons(password, "testuser", "13800138000");
            long endTime = System.nanoTime();
            
            System.out.println("检查结果: " + result);
            System.out.println("执行时间: " + TimeUnit.NANOSECONDS.toMicros(endTime - startTime) + "μs");
        } catch (IOException e) {
            System.err.println("检查失败: " + e.getMessage());
        }
    }

    /**
     * 测试缓存管理功能
     */
    public void testCacheManagement() {
        System.out.println("=== 缓存管理测试 ===");
        
        // 测试缓存清除
        System.out.println("\n1. 测试缓存清除:");
        long startTime = System.currentTimeMillis();
        passwordCheckUtils.clearPasswordCache();
        long clearTime = System.currentTimeMillis() - startTime;
        System.out.println("缓存清除时间: " + clearTime + "ms");

        // 测试缓存预热
        System.out.println("\n2. 测试缓存预热:");
        startTime = System.currentTimeMillis();
        passwordCheckUtils.warmUpCache();
        long warmupTime = System.currentTimeMillis() - startTime;
        System.out.println("缓存预热时间: " + warmupTime + "ms");

        System.out.println("\n=== 缓存管理测试完成 ===");
    }
} 