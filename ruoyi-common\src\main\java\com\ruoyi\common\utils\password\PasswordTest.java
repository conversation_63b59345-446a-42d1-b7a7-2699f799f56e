package com.ruoyi.common.utils.password;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 密码检查测试类
 * 用于验证修复后的密码检查功能
 */
@Component
public class PasswordTest {

    @Autowired
    private GzgWeakPasswordCheckUtils passwordCheckUtils;

    /**
     * 测试密码检查功能
     */
    public void testPasswordCheck() {
        try {
            System.out.println("=== 密码检查功能测试 ===");
            
            // 测试弱密码
            String weakPassword = "password123";
            String result1 = passwordCheckUtils.getGzgWeakPasswordReasons(weakPassword, "testuser", "13800138000");
            System.out.println("弱密码测试: " + result1);
            
            // 测试强密码
            String strongPassword = "StrongP@ssw0rd2024!";
            String result2 = passwordCheckUtils.getGzgWeakPasswordReasons(strongPassword, "testuser", "13800138000");
            System.out.println("强密码测试: " + result2);
            
            // 测试包含用户名的密码
            String userPassword = "testuser123";
            String result3 = passwordCheckUtils.getGzgWeakPasswordReasons(userPassword, "testuser", "13800138000");
            System.out.println("包含用户名测试: " + result3);
            
            System.out.println("=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试缓存功能
     */
    public void testCacheFunctionality() {
        try {
            System.out.println("=== 缓存功能测试 ===");
            
            // 预热缓存
            System.out.println("预热缓存...");
            passwordCheckUtils.warmUpCache();
            System.out.println("缓存预热完成");
            
            // 测试密码检查（应该使用缓存）
            String password = "testpassword123";
            long startTime = System.currentTimeMillis();
            String result = passwordCheckUtils.getGzgWeakPasswordReasons(password, "testuser", "13800138000");
            long endTime = System.currentTimeMillis();
            
            System.out.println("密码检查结果: " + result);
            System.out.println("检查耗时: " + (endTime - startTime) + "ms");
            
            // 清除缓存
            System.out.println("清除缓存...");
            passwordCheckUtils.clearPasswordCache();
            System.out.println("缓存清除完成");
            
            System.out.println("=== 缓存功能测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("缓存测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试静态方法调用修复
     */
    public void testStaticMethodFix() {
        try {
            System.out.println("=== 静态方法调用修复测试 ===");
            
            // 测试实例方法调用（修复后的方式）
            String password = "testpassword123";
            String username = "testuser";
            String phone = "13800138000";
            
            long startTime = System.currentTimeMillis();
            String result = passwordCheckUtils.getGzgWeakPasswordReasons(password, username, phone);
            long endTime = System.currentTimeMillis();
            
            System.out.println("密码检查结果: " + result);
            System.out.println("检查耗时: " + (endTime - startTime) + "ms");
            System.out.println("静态方法调用修复测试通过！");
            
        } catch (Exception e) {
            System.err.println("静态方法调用修复测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 