package com.ruoyi.framework.config;

import com.ruoyi.common.utils.password.GzgWeakPasswordCheckUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 密码缓存预热配置
 * 在应用启动时自动预热密码相关的缓存数据
 */
@Component
public class PasswordCacheConfig implements CommandLineRunner {

    @Autowired
    private GzgWeakPasswordCheckUtils passwordCheckUtils;

    @Override
    public void run(String... args) throws Exception {
        // 应用启动时预热密码缓存（使用轻量级预热避免内存问题）
        System.out.println("开始预热密码检查缓存...");
        try {
            passwordCheckUtils.warmUpCacheLight();
            System.out.println("密码检查缓存预热完成");
        } catch (Exception e) {
            System.err.println("密码检查缓存预热失败: " + e.getMessage());
            // 不抛出异常，避免影响应用启动
        }
    }
} 