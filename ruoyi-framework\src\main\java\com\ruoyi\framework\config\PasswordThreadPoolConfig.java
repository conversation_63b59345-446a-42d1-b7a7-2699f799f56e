package com.ruoyi.framework.config;

import com.ruoyi.common.utils.password.GzgWeakPasswordCheckUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import jakarta.annotation.PreDestroy;

/**
 * 密码检查线程池配置
 * 负责管理密码检查的线程池生命周期
 */
@Configuration
@DependsOn("gzgWeakPasswordCheckUtils")
public class PasswordThreadPoolConfig {

    @Autowired
    private GzgWeakPasswordCheckUtils gzgWeakPasswordCheckUtils;

    /**
     * 应用关闭时优雅地关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        if (gzgWeakPasswordCheckUtils != null) {
            gzgWeakPasswordCheckUtils.shutdown();
        }
    }
} 