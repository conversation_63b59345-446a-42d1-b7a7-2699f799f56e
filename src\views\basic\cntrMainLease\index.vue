<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
            <el-form-item label="箱号" prop="cntrNo">
                <el-input
                    v-model="queryParams.cntrNo"
                    placeholder="请输入箱号"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"/>
            </el-form-item>
            <el-form-item label="尺寸" prop="cntrSize">
                <el-input
                    v-model="queryParams.cntrSize"
                    placeholder="请输入尺寸"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"/>
            </el-form-item>
            <el-form-item label="箱型" prop="cntrType">
                <el-input
                    v-model="queryParams.cntrType"
                    placeholder="请输入箱型"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"/>
            </el-form-item>
            <el-form-item label="起租地点" prop="leaseStartLocation">
                <el-input
                    v-model="queryParams.leaseStartLocation"
                    placeholder="请输入起租地点"
                    clearable
                    style="width: 200px"
                    @keyup.enter="handleQuery"/>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    plain
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['basic:cntrMainLease:add']"
                >新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="success"
                    plain
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['basic:cntrMainLease:edit']"
                >修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['basic:cntrMainLease:remove']"
                >删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="cntrList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="箱号" align="center" prop="cntrNo"/>
            <el-table-column label="尺寸" align="center" prop="cntrSize"/>
            <el-table-column label="箱型" align="center" prop="cntrType"/>
            <el-table-column label="空箱重量(kg)" align="center" prop="tareWeight"/>
            <el-table-column label="最大总重量(kg)" align="center" prop="maxGrossWeight"/>
            <el-table-column label="箱属" align="center" prop="cntrOwnership"/>
            <el-table-column label="起租时间" align="center" prop="leaseStartTime" width="120">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.leaseStartTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="租赁期" align="center" prop="leasePeriod"/>
            <el-table-column label="起租地点" align="center" prop="leaseStartLocation"/>
            <el-table-column label="还箱地点" align="center" prop="returnLocation"/>
            <el-table-column label="默认货类" align="center" prop="defaultCargoType"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        link
                        type="primary"
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['basic:cntrMainLease:edit']"
                    >修改</el-button>
                    <el-button
                        link
                        type="danger"
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['basic:cntrMainLease:remove']"
                    >删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination
            v-show="total>0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />

        <!-- 新增修改对话框 -->
        <el-dialog v-model="dialogOpen" :title="title" width="1200px" append-to-body :close-on-click-modal="false" :show-close="false">
            <el-form ref="cntrRef" :model="form" :rules="rules" label-width="120px">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="8">
                        <el-form-item label="箱号：" prop="cntrNo">
                            <el-input v-model="form.cntrNo" placeholder="请输入箱号" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="尺寸：" prop="cntrSize">
                            <el-input v-model="form.cntrSize" placeholder="请输入尺寸" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="箱型：" prop="cntrType">
                            <el-input v-model="form.cntrType" placeholder="请输入箱型" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="8">
                        <el-form-item label="空箱重量：" prop="tareWeight">
                            <el-input-number v-model="form.tareWeight" placeholder="请输入空箱重量" :precision="2" style="width: 100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="最大总重量：" prop="maxGrossWeight">
                            <el-input-number v-model="form.maxGrossWeight" placeholder="请输入最大总重量" :precision="2" style="width: 100%" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="箱属：" prop="cntrOwnership">
                            <el-input v-model="form.cntrOwnership" placeholder="请输入箱属" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="8">
                        <el-form-item label="起租时间：" prop="leaseStartTime">
                            <el-date-picker
                                v-model="form.leaseStartTime"
                                type="date"
                                placeholder="选择起租时间"
                                style="width: 100%">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="租赁期：" prop="leasePeriod">
                            <el-input v-model="form.leasePeriod" placeholder="请输入租赁期" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="起租地点：" prop="leaseStartLocation">
                            <el-input v-model="form.leaseStartLocation" placeholder="请输入起租地点" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="8">
                        <el-form-item label="还箱地点：" prop="returnLocation">
                            <el-input v-model="form.returnLocation" placeholder="请输入还箱地点" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="默认货类：" prop="defaultCargoType">
                            <el-input v-model="form.defaultCargoType" placeholder="请输入默认货类" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="备注：" prop="remark">
                            <el-input v-model="form.remark" placeholder="请输入备注" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { list, addBasicCntrMainLease, editBasicCntrMainLease, deleteBasicCntrMainLease, getById } from '@/api/basic/cntrMainLease'
import { parseTime } from '@/utils/ruoyi'

export default {
    name: "CntrMainLease",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 租赁箱表格数据
            cntrList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            dialogOpen: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                cntrNo: null,
                cntrSize: null,
                cntrType: null,
                leaseStartLocation: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                cntrNo: [
                    { required: true, message: "箱号不能为空", trigger: "blur" }
                ]
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        // 解析时间
        parseTime,

        /** 查询租赁箱列表 */
        getList() {
            this.loading = true;
            list(this.queryParams).then(response => {
                this.cntrList = response.records;
                this.total = response.totalRow;
                this.loading = false;
            });
        },

        // 取消按钮
        cancel() {
            this.dialogOpen = false;
            this.reset();
        },

        // 表单重置
        reset() {
            this.form = {
                id: null,
                cntrNo: null,
                cntrSize: null,
                cntrType: null,
                tareWeight: null,
                maxGrossWeight: null,
                cntrOwnership: null,
                leaseStartTime: null,
                leasePeriod: null,
                leaseStartLocation: null,
                returnLocation: null,
                defaultCargoType: null,
                attachmentPath: null
            };
            this.resetForm("cntrRef");
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },

        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryRef");
            this.handleQuery();
        },

        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },

        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.dialogOpen = true;
            this.title = "添加租赁箱信息";
        },

        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids[0];
            getById(id).then(response => {
                this.form = response.data;
                this.dialogOpen = true;
                this.title = "修改租赁箱信息";
            });
        },

        /** 提交按钮 */
        submitForm() {
            this.$refs["cntrRef"].validate(valid => {
                if (valid) {
                    if (this.form.id != null) {
                        editBasicCntrMainLease(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.dialogOpen = false;
                            this.getList();
                        });
                    } else {
                        addBasicCntrMainLease(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.dialogOpen = false;
                            this.getList();
                        });
                    }
                }
            });
        },

        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id ? [row.id] : this.ids;
            this.$modal.confirm('是否确认删除租赁箱编号为"' + ids + '"的数据项？').then(function() {
                return deleteBasicCntrMainLease(ids);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        }
    }
};
</script>
