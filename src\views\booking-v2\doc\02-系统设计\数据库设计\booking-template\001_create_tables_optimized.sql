-- ====================================
-- 订舱模板功能 - 优化版数据表创建脚本
-- 创建时间: 2025-08-04
-- 版本: v2.0 (优化版)
-- 描述: 创建订舱模板相关的数据表(简化设计)
-- 适用数据库: Oracle 11g+
-- 优化点: 
--   1. 去掉复杂的usage_log表，用last_used_time字段替代
--   2. 去掉shared_users字段，使用若依数据权限
--   3. 保持BaseEntity规范，包含remark字段
-- ====================================

-- 1. 订舱模板主表(优化版)
CREATE TABLE booking_template (
    id VARCHAR2(64) NOT NULL,
    template_name VARCHAR2(100) NOT NULL,
    template_type VARCHAR2(20) NOT NULL CHECK (template_type IN ('PUBLIC', 'PRIVATE')),
    template_data CLOB NOT NULL,
    description VARCHAR2(500),
    usage_count NUMBER(10) DEFAULT 0,
    last_used_time DATE,
    is_active CHAR(1) DEFAULT '1' CHECK (is_active IN ('0', '1')),
    -- BaseEntity标准字段
    create_by VARCHAR2(64),
    create_time DATE DEFAULT SYSDATE,
    update_by VARCHAR2(64), 
    update_time DATE DEFAULT SYSDATE,
    del_flag CHAR(1) DEFAULT '0' CHECK (del_flag IN ('0', '2')),
    version NUMBER(10) DEFAULT 1,
    remark VARCHAR2(500),
    CONSTRAINT pk_booking_template PRIMARY KEY (id)
);

-- 2. 模板标签表(保持不变)
CREATE TABLE booking_template_tag (
    id VARCHAR2(64) NOT NULL,
    tag_name VARCHAR2(50) NOT NULL,
    tag_color VARCHAR2(20) DEFAULT '#409eff',
    usage_count NUMBER(10) DEFAULT 0,
    -- BaseEntity标准字段
    create_by VARCHAR2(64),
    create_time DATE DEFAULT SYSDATE,
    update_by VARCHAR2(64),
    update_time DATE DEFAULT SYSDATE,
    del_flag CHAR(1) DEFAULT '0' CHECK (del_flag IN ('0', '2')),
    version NUMBER(10) DEFAULT 1,
    remark VARCHAR2(500),
    CONSTRAINT pk_booking_template_tag PRIMARY KEY (id),
    CONSTRAINT uk_tag_name UNIQUE (tag_name)
);

-- 3. 模板标签关联表(简化)
CREATE TABLE booking_template_tag_rel (
    template_id VARCHAR2(64) NOT NULL,
    tag_id VARCHAR2(64) NOT NULL,
    create_time DATE DEFAULT SYSDATE,
    CONSTRAINT pk_template_tag_rel PRIMARY KEY (template_id, tag_id),
    CONSTRAINT fk_template_tag_template FOREIGN KEY (template_id) REFERENCES booking_template(id) ON DELETE CASCADE,
    CONSTRAINT fk_template_tag_tag FOREIGN KEY (tag_id) REFERENCES booking_template_tag(id) ON DELETE CASCADE
);

-- 添加表注释 
COMMENT ON TABLE booking_template IS '订舱模板主表-优化版';
COMMENT ON TABLE booking_template_tag IS '模板标签字典表';  
COMMENT ON TABLE booking_template_tag_rel IS '模板标签关联表';

-- 添加字段注释
COMMENT ON COLUMN booking_template.id IS '主键ID-雪花算法';
COMMENT ON COLUMN booking_template.template_name IS '模板名称';
COMMENT ON COLUMN booking_template.template_type IS '模板类型: PUBLIC=公共模板, PRIVATE=私有模板';
COMMENT ON COLUMN booking_template.template_data IS '模板数据JSON(完整的EntrustDTO)';
COMMENT ON COLUMN booking_template.description IS '模板描述';
COMMENT ON COLUMN booking_template.usage_count IS '使用次数统计';
COMMENT ON COLUMN booking_template.last_used_time IS '最后使用时间(替代usage_log表)';
COMMENT ON COLUMN booking_template.is_active IS '是否激活: 1=激活, 0=停用';
COMMENT ON COLUMN booking_template.create_by IS '创建人(若依数据权限依赖字段)';
COMMENT ON COLUMN booking_template.create_time IS '创建时间';
COMMENT ON COLUMN booking_template.update_by IS '更新人';  
COMMENT ON COLUMN booking_template.update_time IS '更新时间';
COMMENT ON COLUMN booking_template.del_flag IS '删除标志: 0=正常, 2=删除(若依逻辑删除)';
COMMENT ON COLUMN booking_template.version IS '版本号(乐观锁)';
COMMENT ON COLUMN booking_template.remark IS 'BaseEntity备注字段';

COMMENT ON COLUMN booking_template_tag.id IS '标签ID';
COMMENT ON COLUMN booking_template_tag.tag_name IS '标签名称';
COMMENT ON COLUMN booking_template_tag.tag_color IS '标签颜色(十六进制)';
COMMENT ON COLUMN booking_template_tag.usage_count IS '标签使用次数';
COMMENT ON COLUMN booking_template_tag.create_by IS '创建人';
COMMENT ON COLUMN booking_template_tag.create_time IS '创建时间';
COMMENT ON COLUMN booking_template_tag.update_by IS '更新人';
COMMENT ON COLUMN booking_template_tag.update_time IS '更新时间';
COMMENT ON COLUMN booking_template_tag.del_flag IS '删除标志: 0=正常, 2=删除';
COMMENT ON COLUMN booking_template_tag.version IS '版本号'; 
COMMENT ON COLUMN booking_template_tag.remark IS 'BaseEntity备注字段';

COMMENT ON COLUMN booking_template_tag_rel.template_id IS '模板ID';
COMMENT ON COLUMN booking_template_tag_rel.tag_id IS '标签ID';
COMMENT ON COLUMN booking_template_tag_rel.create_time IS '关联创建时间';

-- 执行完成提示
SELECT '订舱模板功能数据表创建完成(优化版)!' AS RESULT FROM DUAL;