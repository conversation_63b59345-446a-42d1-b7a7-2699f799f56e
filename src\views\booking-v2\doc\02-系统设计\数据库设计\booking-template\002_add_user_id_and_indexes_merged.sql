-- ====================================
-- 订舱模板功能 - 用户ID字段增强 + 优化索引脚本 (合并版)
-- 创建时间: 2025-08-04
-- 版本: v2.1 (用户ID增强 + 索引优化)
-- 描述: 为模板表增加用户ID字段并创建性能优化索引
-- 适用数据库: Oracle 11g+
-- 优化目标: 
--   1. 解决用户名重复问题
--   2. 提高权限控制精度
--   3. 支持用户名变更场景
--   4. 针对分离接口设计优化索引
--   5. 基于若依数据权限优化查询索引
-- ====================================

-- ==================================
-- 第一部分: 用户ID字段增强
-- ==================================

-- 1. 为booking_template表添加用户ID字段
ALTER TABLE booking_template ADD (
    create_by_id NUMBER(20),
    update_by_id NUMBER(20)
);

-- 2. 为booking_template_tag表添加用户ID字段  
ALTER TABLE booking_template_tag ADD (
    create_by_id NUMBER(20),
    update_by_id NUMBER(20)
);

-- 3. 添加字段注释
COMMENT ON COLUMN booking_template.create_by_id IS '创建用户ID(主键，精确权限控制)';
COMMENT ON COLUMN booking_template.update_by_id IS '更新用户ID(主键，精确权限控制)';
COMMENT ON COLUMN booking_template.create_by IS '创建用户名(保留兼容性，display only)';
COMMENT ON COLUMN booking_template.update_by IS '更新用户名(保留兼容性，display only)';

COMMENT ON COLUMN booking_template_tag.create_by_id IS '创建用户ID(主键，精确权限控制)';
COMMENT ON COLUMN booking_template_tag.update_by_id IS '更新用户ID(主键，精确权限控制)';
COMMENT ON COLUMN booking_template_tag.create_by IS '创建用户名(保留兼容性，display only)';
COMMENT ON COLUMN booking_template_tag.update_by IS '更新用户名(保留兼容性，display only)';

-- ==================================
-- 第二部分: 清理旧索引
-- ==================================

-- 删除可能存在的旧索引(忽略错误)  
BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_TYPE_ACTIVE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_CREATOR_TYPE';  
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_USAGE_TIME';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_NAME_SEARCH';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TAG_NAME_USAGE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX idx_booking_template_user_type';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX idx_booking_template_tag_user';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

-- ==================================
-- 第三部分: 创建优化索引
-- ==================================

-- 订舱模板主表索引(针对分离接口优化)

-- 1. 公共模板查询索引(关闭数据权限场景)
CREATE INDEX IDX_TEMPLATE_TYPE_ACTIVE ON booking_template(template_type, is_active, del_flag);

-- 2. 私有模板查询索引(开启数据权限场景) - 支持用户名兼容
CREATE INDEX IDX_TEMPLATE_CREATOR_TYPE ON booking_template(create_by, template_type, del_flag);

-- 3. 私有模板查询索引(用户ID版本) - 新增精确权限控制
CREATE INDEX IDX_TEMPLATE_CREATOR_ID_TYPE ON booking_template(create_by_id, template_type, del_flag);

-- 4. 使用频率排序索引
CREATE INDEX IDX_TEMPLATE_USAGE_TIME ON booking_template(usage_count DESC, last_used_time DESC, create_time DESC);

-- 5. 模板名称搜索索引(支持模糊查询)
CREATE INDEX IDX_TEMPLATE_NAME_SEARCH ON booking_template(template_name, del_flag);

-- 6. 激活状态索引
CREATE INDEX IDX_TEMPLATE_ACTIVE_FLAG ON booking_template(is_active, del_flag);

-- 标签表索引  

-- 7. 标签使用频率索引
CREATE INDEX IDX_TAG_NAME_USAGE ON booking_template_tag(usage_count DESC, tag_name, del_flag);

-- 8. 标签名称查询索引
CREATE INDEX IDX_TAG_NAME_QUERY ON booking_template_tag(tag_name, del_flag);

-- 9. 标签用户ID权限索引
CREATE INDEX IDX_TAG_USER_ID ON booking_template_tag(create_by_id, del_flag);

-- 关联表索引

-- 10. 模板标签关联查询索引
CREATE INDEX IDX_TEMPLATE_TAG_REL_TEMPLATE ON booking_template_tag_rel(template_id, create_time DESC);
CREATE INDEX IDX_TEMPLATE_TAG_REL_TAG ON booking_template_tag_rel(tag_id, create_time DESC);

-- 复合索引(高频查询场景)

-- 11. 公共模板完整查询索引
CREATE INDEX IDX_PUBLIC_TEMPLATE_QUERY ON booking_template(template_type, is_active, del_flag, usage_count DESC);

-- 12. 私有模板完整查询索引(若依数据权限场景 - 用户名版本)
CREATE INDEX IDX_PRIVATE_TEMPLATE_QUERY ON booking_template(create_by, template_type, is_active, del_flag, create_time DESC);

-- 13. 私有模板完整查询索引(用户ID版本) - 新增精确权限控制
CREATE INDEX IDX_PRIVATE_TEMPLATE_QUERY_ID ON booking_template(create_by_id, template_type, is_active, del_flag, create_time DESC);

-- ==================================
-- 第四部分: 数据迁移示例
-- ==================================

-- 数据迁移示例(如果有历史数据需要迁移)
/*
-- 假设现有数据需要迁移用户ID，可以通过以下SQL更新
-- 注意：实际迁移时需要根据具体的用户表结构调整

UPDATE booking_template 
SET create_by_id = (
    SELECT user_id 
    FROM sys_user 
    WHERE user_name = booking_template.create_by 
    AND del_flag = '0'
    AND ROWNUM = 1
)
WHERE create_by_id IS NULL AND create_by IS NOT NULL;

UPDATE booking_template 
SET update_by_id = (
    SELECT user_id 
    FROM sys_user 
    WHERE user_name = booking_template.update_by 
    AND del_flag = '0'
    AND ROWNUM = 1
)
WHERE update_by_id IS NULL AND update_by IS NOT NULL;

-- 标签表数据迁移
UPDATE booking_template_tag 
SET create_by_id = (
    SELECT user_id 
    FROM sys_user 
    WHERE user_name = booking_template_tag.create_by 
    AND del_flag = '0'
    AND ROWNUM = 1
)
WHERE create_by_id IS NULL AND create_by IS NOT NULL;
*/

-- ==================================
-- 第五部分: 权限控制策略说明
-- ==================================

/*
权限控制逻辑(双重保障):
1. 主要控制: 使用create_by_id(用户ID)进行精确权限过滤
2. 备用兼容: 保留create_by(用户名)用于显示和向后兼容

查询私有模板的WHERE条件:
- 精确模式: WHERE template_type = 'PRIVATE' AND create_by_id = #{currentUserId}
- 兼容模式: WHERE template_type = 'PRIVATE' AND (create_by_id = #{currentUserId} OR create_by = #{currentUsername})

优势:
- 解决用户名重复问题
- 支持用户名变更场景  
- 提升查询性能(数值比较比字符串比较快)
- 保持向后兼容性

索引使用说明:

公共模板查询(Controller: @DataPermission(enable = false))
SELECT * FROM booking_template WHERE template_type = 'PUBLIC' AND is_active = '1' AND del_flag = '0'
使用索引: IDX_PUBLIC_TEMPLATE_QUERY

私有模板查询(Controller: @DataPermission) - 用户名版本
SELECT * FROM booking_template WHERE create_by = ? AND template_type = 'PRIVATE' AND del_flag = '0'
使用索引: IDX_PRIVATE_TEMPLATE_QUERY

私有模板查询(Controller: @DataPermission) - 用户ID版本 (推荐)
SELECT * FROM booking_template WHERE create_by_id = ? AND template_type = 'PRIVATE' AND del_flag = '0'
使用索引: IDX_PRIVATE_TEMPLATE_QUERY_ID

模板名称搜索
SELECT * FROM booking_template WHERE template_name LIKE '%关键词%' AND del_flag = '0'
使用索引: IDX_TEMPLATE_NAME_SEARCH

使用频率排序
SELECT * FROM booking_template ORDER BY usage_count DESC, last_used_time DESC
使用索引: IDX_TEMPLATE_USAGE_TIME
*/

-- 执行完成提示
SELECT '订舱模板功能用户ID字段增强和索引优化完成!' AS RESULT FROM DUAL;