-- ====================================
-- 订舱模板功能 - 优化版默认数据插入脚本
-- 创建时间: 2025-08-04
-- 版本: v2.0 (优化版)
-- 描述: 插入订舱模板功能的默认数据
-- 优化点:
--   1. 模板数据使用真实的EntrustDTO结构
--   2. 新增更多实用的默认标签
--   3. 优化示例模板的业务场景
-- ====================================

-- 1. 插入默认标签(基于船务业务场景)
INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_001', '海运', '#409eff', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '海运运输相关模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_002', '陆运', '#67c23a', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '陆运运输相关模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_003', '出口', '#e6a23c', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '出口贸易相关模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_004', '进口', '#f56c6c', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '进口贸易相关模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_005', '内贸', '#909399', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '内贸运输相关模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_006', '集装箱', '#ff6b6b', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '集装箱运输相关模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_007', '常用', '#722ed1', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '常用业务模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_008', '危险品', '#fa541c', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '危险品运输相关模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_009', '冷藏', '#13c2c2', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '冷藏货物相关模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_010', '超限', '#eb2f96', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '超限货物相关模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_011', '两湾快航', '#52c41a', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '两湾快航专线模板');

INSERT INTO booking_template_tag (id, tag_name, tag_color, usage_count, create_by, create_time, update_by, update_time, del_flag, version, remark) VALUES 
('BT_TAG_012', '黄埔共同体', '#1890ff', 0, 'system', SYSDATE, 'system', SYSDATE, '0', 1, '黄埔共同体业务模板');

-- 2. 插入示例公共模板 - 标准海运出口模板
INSERT INTO booking_template (
    id, template_name, template_type, template_data, description, 
    usage_count, last_used_time, is_active, 
    create_by, create_time, update_by, update_time, del_flag, version, remark
) VALUES (
    'BT_TEMP_001', 
    '标准海运出口模板', 
    'PUBLIC',
    '{
      "orderMain": {
        "id": null,
        "orderNo": "",
        "shipperId": null,
        "shipperName": "",
        "orderDate": null,
        "businessType": "WATERWAY",
        "tradeType": "EXPORT",
        "consortium": "",
        "customerAgreement": "",
        "settlementMethod": "MONTHLY",
        "totalContainers": 0,
        "totalWeight": 0,
        "status": "DRAFT",
        "remark": "",
        "delFlag": "0",
        "version": 1
      },
      "bookingMain": {
        "id": null,
        "orderId": null,
        "bookingNumber": "",
        "status": "DRAFT",
        "shipperId": null,
        "shipperName": "",
        "originLocationId": null,
        "originLocationName": "",
        "destinationLocationId": null,
        "destinationLocationName": "",
        "bookingDate": null,
        "departureDate": null,
        "deliveryDate": null,
        "loadingTerminalId": null,
        "loadingTerminalName": "",
        "unloadingTerminalId": null,
        "unloadingTerminalName": "",
        "loadingAgentId": null,
        "loadingAgentName": "",
        "unloadingAgentId": null,
        "unloadingAgentName": "",
        "tradeType": "EXPORT",
        "transportMode": "",
        "customsType": "GENERAL",
        "settlementMethod": "MONTHLY",
        "customerAgreement": "",
        "consignmentSource": "STAFF_ENTRY",
        "remark": "",
        "delFlag": "0",
        "version": 1
      },
      "bookingCntrNumList": [{
        "id": null,
        "bookingId": null,
        "containerSize": "20",
        "containerType": "GP",
        "isEmpty": "0",
        "quantity": 1,
        "isDangerous": "0",
        "dangerousLevel": "",
        "isRefrigerated": "0",
        "cargoType": "",
        "singleWeight": 0,
        "totalWeight": 0,
        "isOversize": "0",
        "oversizeDimensions": "",
        "remark": "",
        "delFlag": "0",
        "version": 1
      }],
      "bookingTransitPortList": [],
      "logisticsMainList": [{
        "id": null,
        "bookingId": null,
        "businessType": "WATERWAY",
        "supplierId": null,
        "supplierName": "",
        "originLocationId": null,
        "originLocationName": "",
        "destinationLocationId": null,
        "destinationLocationName": "",
        "containerDetails": "",
        "termsConditions": "",
        "requiredDepartureDate": null,
        "requiredArrivalDate": null,
        "version": 1
      }]
    }',
    '适用于标准海运出口业务的基础模板，包含常用的20尺普通集装箱配置', 
    0, NULL, '1', 
    'admin', SYSDATE, 'admin', SYSDATE, '0', 1,
    '系统预置的海运出口标准模板'
);

-- 3. 插入示例公共模板 - 内贸快航模板  
INSERT INTO booking_template (
    id, template_name, template_type, template_data, description,
    usage_count, last_used_time, is_active,
    create_by, create_time, update_by, update_time, del_flag, version, remark
) VALUES (
    'BT_TEMP_002',
    '内贸快航模板', 
    'PUBLIC',
    '{
      "orderMain": {
        "id": null,
        "orderNo": "",
        "businessType": "WATERWAY", 
        "tradeType": "DOMESTIC",
        "consortium": "黄埔共同体",
        "customerAgreement": "",
        "settlementMethod": "MONTHLY",
        "totalContainers": 0,
        "totalWeight": 0,
        "status": "DRAFT",
        "delFlag": "0",
        "version": 1
      },
      "bookingMain": {
        "id": null,
        "bookingNumber": "",
        "status": "DRAFT",
        "tradeType": "DOMESTIC",
        "transportMode": "FULL_OUT_EMPTY_BACK",
        "customsType": "GENERAL", 
        "settlementMethod": "MONTHLY",
        "consignmentSource": "STAFF_ENTRY",
        "delFlag": "0",
        "version": 1
      },
      "bookingCntrNumList": [{
        "containerSize": "40",
        "containerType": "GP", 
        "isEmpty": "0",
        "quantity": 1,
        "isDangerous": "0",
        "isRefrigerated": "0",
        "isOversize": "0",
        "delFlag": "0",
        "version": 1
      }],
      "bookingTransitPortList": [],
      "logisticsMainList": [{
        "businessType": "WATERWAY",
        "termsConditions": "内贸快航服务条款",
        "version": 1
      }]
    }',
    '适用于内贸快航业务，支持黄埔共同体模式的标准模板',
    0, NULL, '1',
    'admin', SYSDATE, 'admin', SYSDATE, '0', 1, 
    '内贸快航专用业务模板'
);

-- 4. 关联模板与标签 - 海运出口模板
INSERT INTO booking_template_tag_rel (template_id, tag_id, create_time) VALUES ('BT_TEMP_001', 'BT_TAG_001', SYSDATE); -- 海运
INSERT INTO booking_template_tag_rel (template_id, tag_id, create_time) VALUES ('BT_TEMP_001', 'BT_TAG_003', SYSDATE); -- 出口  
INSERT INTO booking_template_tag_rel (template_id, tag_id, create_time) VALUES ('BT_TEMP_001', 'BT_TAG_006', SYSDATE); -- 集装箱
INSERT INTO booking_template_tag_rel (template_id, tag_id, create_time) VALUES ('BT_TEMP_001', 'BT_TAG_007', SYSDATE); -- 常用

-- 5. 关联模板与标签 - 内贸快航模板
INSERT INTO booking_template_tag_rel (template_id, tag_id, create_time) VALUES ('BT_TEMP_002', 'BT_TAG_001', SYSDATE); -- 海运
INSERT INTO booking_template_tag_rel (template_id, tag_id, create_time) VALUES ('BT_TEMP_002', 'BT_TAG_005', SYSDATE); -- 内贸
INSERT INTO booking_template_tag_rel (template_id, tag_id, create_time) VALUES ('BT_TEMP_002', 'BT_TAG_011', SYSDATE); -- 两湾快航
INSERT INTO booking_template_tag_rel (template_id, tag_id, create_time) VALUES ('BT_TEMP_002', 'BT_TAG_012', SYSDATE); -- 黄埔共同体

-- 提交事务
COMMIT;

-- 验证数据插入
SELECT 
    '已插入' || COUNT(*) || '个默认标签' AS tag_info
FROM booking_template_tag 
WHERE create_by = 'system' AND del_flag = '0';

SELECT 
    '已插入' || COUNT(*) || '个示例模板' AS template_info
FROM booking_template 
WHERE create_by = 'admin' AND template_type = 'PUBLIC' AND del_flag = '0';

SELECT 
    '已创建' || COUNT(*) || '个标签关联关系' AS relation_info
FROM booking_template_tag_rel;

-- 执行完成提示
SELECT '订舱模板功能默认数据插入完成(优化版)!' AS RESULT FROM DUAL;