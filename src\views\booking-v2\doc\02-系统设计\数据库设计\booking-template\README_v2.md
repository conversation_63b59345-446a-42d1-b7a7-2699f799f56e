# 📋 订舱模板功能 - 优化版设计文档

## 🎯 优化版特性

### 主要改进
- ✅ **去掉复杂的usage_log表**: 使用`last_used_time`字段替代，简化设计
- ✅ **去掉shared_users字段**: 依靠若依数据权限实现权限控制
- ✅ **完整继承BaseEntity**: 包含`remark`字段，符合若依规范
- ✅ **针对分离接口优化**: 支持公共/私有模板分别查询
- ✅ **真实业务数据**: 使用完整的EntrustDTO结构作为模板数据

## 📁 脚本文件说明

### 🔧 安装脚本 (按顺序执行)
| 脚本文件 | 说明 | 执行顺序 |
|---------|------|---------|
| `001_create_tables_optimized.sql` | 创建优化版数据表 | ✅ 1 |
| `002_create_indexes_optimized.sql` | 创建性能优化索引 | ✅ 2 |
| `003_insert_default_data_optimized.sql` | 插入默认数据 | ✅ 3 |

### 🔄 回滚脚本
| 脚本文件 | 说明 | 用途 |
|---------|------|------|
| `rollback/001_drop_tables.sql` | 删除所有表和数据 | 完全回滚 |

## 🗄️ 优化后数据表结构

### 1. booking_template - 模板主表
```sql
-- 核心业务字段
id VARCHAR2(64)              -- 主键ID-雪花算法
template_name VARCHAR2(100)  -- 模板名称
template_type VARCHAR2(20)   -- PUBLIC/PRIVATE
template_data CLOB           -- 完整EntrustDTO JSON数据
description VARCHAR2(500)    -- 模板描述
usage_count NUMBER(10)       -- 使用次数统计
last_used_time DATE          -- 最后使用时间(替代usage_log表)
is_active CHAR(1)           -- 是否激活

-- BaseEntity标准字段
create_by VARCHAR2(64)       -- 创建人(若依数据权限关键字段)
create_time DATE            -- 创建时间
update_by VARCHAR2(64)      -- 更新人
update_time DATE           -- 更新时间
del_flag CHAR(1)           -- 逻辑删除标志
version NUMBER(10)         -- 乐观锁版本号
remark VARCHAR2(500)       -- 备注字段
```

### 2. booking_template_tag - 标签表
- 用户自定义标签管理
- 支持标签颜色和使用统计
- 完整继承BaseEntity

### 3. booking_template_tag_rel - 标签关联表
- 模板与标签的多对多关系
- 简化设计，只保留核心关联字段

## 🚀 权限控制策略

### 若依数据权限集成
```java
/**
 * 查询公共模板 - 所有人可见
 */
@GetMapping("/public/list")
@DataPermission(enable = false)  // 关闭数据权限过滤
public AjaxResult listPublicTemplates() {
    // 查询 template_type = 'PUBLIC' 的所有模板
}

/**
 * 查询私有模板 - 只能看到自己的
 */
@GetMapping("/private/list")  
@DataPermission  // 自动添加 create_by = 当前用户ID
public AjaxResult listPrivateTemplates() {
    // 自动过滤为当前用户创建的私有模板
}
```

### 权限矩阵
| 模板类型 | 创建权限 | 查看权限 | 编辑权限 | 删除权限 |
|---------|---------|---------|---------|---------|
| 公共模板 | 管理员 | 所有人 | 管理员 | 管理员 |
| 私有模板 | 所有人 | 创建者 | 创建者 | 创建者 |

## 📊 性能优化设计

### 关键索引策略
1. **公共模板查询**: `IDX_PUBLIC_TEMPLATE_QUERY`
   - 字段: `(template_type, is_active, del_flag, usage_count DESC)`
   - 场景: 查询所有公共模板并按使用频率排序

2. **私有模板查询**: `IDX_PRIVATE_TEMPLATE_QUERY`  
   - 字段: `(create_by, template_type, is_active, del_flag, create_time DESC)`
   - 场景: 若依数据权限自动添加create_by条件

3. **模板搜索**: `IDX_TEMPLATE_NAME_SEARCH`
   - 字段: `(template_name, del_flag)`
   - 场景: 支持模板名称模糊搜索

4. **使用频率排序**: `IDX_TEMPLATE_USAGE_TIME`
   - 字段: `(usage_count DESC, last_used_time DESC, create_time DESC)`
   - 场景: 热门模板推荐

## 🏷️ 预置标签系统

### 业务标签分类
| 标签名称 | 颜色 | 业务场景 |
|---------|------|---------|
| 海运 | #409eff | 海运运输业务 |
| 陆运 | #67c23a | 陆运运输业务 |
| 出口 | #e6a23c | 出口贸易业务 |
| 进口 | #f56c6c | 进口贸易业务 |
| 内贸 | #909399 | 内贸运输业务 |
| 集装箱 | #ff6b6b | 集装箱运输 |
| 常用 | #722ed1 | 常用业务模板 |
| 危险品 | #fa541c | 危险品运输 |
| 冷藏 | #13c2c2 | 冷藏货物 |
| 超限 | #eb2f96 | 超限货物 |
| 两湾快航 | #52c41a | 两湾快航专线 |
| 黄埔共同体 | #1890ff | 黄埔共同体业务 |

## 🔧 安装部署

### 1. 执行SQL脚本
```sql
-- 连接Oracle数据库
sqlplus username/password@database

-- 按顺序执行脚本
@001_create_tables_optimized.sql
@002_create_indexes_optimized.sql  
@003_insert_default_data_optimized.sql
```

### 2. 验证安装
```sql
-- 检查表创建
SELECT table_name FROM user_tables 
WHERE table_name LIKE 'BOOKING_TEMPLATE%'
ORDER BY table_name;

-- 检查默认数据
SELECT COUNT(*) as tag_count FROM booking_template_tag WHERE del_flag = '0';
SELECT COUNT(*) as template_count FROM booking_template WHERE del_flag = '0';
SELECT COUNT(*) as relation_count FROM booking_template_tag_rel;
```

### 3. 回滚操作
```sql
-- ⚠️ 警告: 此操作将删除所有模板数据!
@rollback/001_drop_tables.sql
```

## ⚠️ 重要说明

### 设计原则
1. **简化优先**: 去掉不必要的复杂设计，专注核心功能
2. **框架集成**: 充分利用若依框架的数据权限能力
3. **性能优化**: 针对分离接口设计专门的索引策略
4. **业务贴合**: 预置标签和示例模板贴合实际船务业务

### 注意事项
1. **数据权限依赖**: `create_by`字段是若依数据权限的关键，不能为空
2. **逻辑删除**: 使用`del_flag`字段实现逻辑删除，符合若依规范
3. **乐观锁**: `version`字段支持并发更新控制
4. **JSON数据**: `template_data`字段存储完整的EntrustDTO结构

## 📞 技术支持

如遇问题请参考：
- 📖 MyBatis-Flex官方文档
- 🗃️ 若依框架文档  
- 💬 项目技术群