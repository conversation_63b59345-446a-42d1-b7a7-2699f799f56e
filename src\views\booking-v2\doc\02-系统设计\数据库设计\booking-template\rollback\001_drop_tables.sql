-- ====================================
-- 订舱模板功能 - 表删除回滚脚本 (优化版)
-- 创建时间: 2025-08-04
-- 版本: v2.0 (基于优化版表结构)
-- 描述: 删除订舱模板相关的所有数据表(回滚操作)
-- 警告: 此操作将删除所有模板数据，请谨慎执行!
-- 适用表结构: 001_create_tables_optimized.sql
-- ====================================

-- 删除前确认提示
SELECT '警告: 即将删除订舱模板功能的所有数据表!' AS WARNING FROM DUAL;
SELECT '如果确认要执行，请继续运行下面的删除语句' AS CONFIRM FROM DUAL;

-- 删除所有相关索引(按优化版索引结构)
BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_TYPE_ACTIVE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_CREATOR_TYPE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_CREATOR_ID_TYPE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_USAGE_TIME';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_NAME_SEARCH';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_ACTIVE_FLAG';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TAG_NAME_USAGE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TAG_NAME_QUERY';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TAG_USER_ID';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_TAG_REL_TEMPLATE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_TAG_REL_TAG';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_PUBLIC_TEMPLATE_QUERY';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_PRIVATE_TEMPLATE_QUERY';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_PRIVATE_TEMPLATE_QUERY_ID';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

-- 删除外键约束的子表(按依赖关系逆序删除)

-- 1. 删除模板标签关联表  
DROP TABLE booking_template_tag_rel CASCADE CONSTRAINTS;

-- 2. 删除模板标签表
DROP TABLE booking_template_tag CASCADE CONSTRAINTS;

-- 3. 删除订舱模板主表
DROP TABLE booking_template CASCADE CONSTRAINTS;

-- 删除相关序列(如果存在)
-- 注意: 雪花ID不需要序列，此处保留用于其他ID策略
-- DROP SEQUENCE seq_booking_template;
-- DROP SEQUENCE seq_booking_template_tag;

-- 验证删除结果
SELECT 
    table_name,
    'TABLE_DROPPED' AS status
FROM user_tables 
WHERE table_name IN (
    'BOOKING_TEMPLATE',
    'BOOKING_TEMPLATE_TAG', 
    'BOOKING_TEMPLATE_TAG_REL'
);

-- 如果上述查询无结果，说明表已成功删除
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '订舱模板功能所有数据表已成功删除!'
        ELSE '删除失败，仍有' || COUNT(*) || '个表存在'
    END AS RESULT
FROM user_tables 
WHERE table_name IN (
    'BOOKING_TEMPLATE',
    'BOOKING_TEMPLATE_TAG', 
    'BOOKING_TEMPLATE_TAG_REL'
);

-- 验证索引删除结果
SELECT 
    index_name,
    'INDEX_DROPPED' AS status
FROM user_indexes 
WHERE index_name LIKE 'IDX_%TEMPLATE%' OR index_name LIKE 'IDX_%TAG%';

-- 执行完成提示
SELECT '订舱模板功能表删除回滚完成(优化版)!' AS RESULT FROM DUAL;