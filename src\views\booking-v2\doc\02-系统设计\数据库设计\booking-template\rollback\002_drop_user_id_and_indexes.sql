-- ====================================
-- 订舱模板功能 - 用户ID字段和索引删除回滚脚本
-- 创建时间: 2025-08-04
-- 版本: v2.1 (用户ID回滚版)
-- 描述: 回滚用户ID字段增强和索引优化操作
-- 警告: 此操作将删除用户ID字段和相关索引!
-- 适用脚本: 002_add_user_id_and_indexes_merged.sql
-- ====================================

-- 删除前确认提示
SELECT '警告: 即将回滚用户ID字段增强和索引优化操作!' AS WARNING FROM DUAL;
SELECT '如果确认要执行，请继续运行下面的回滚语句' AS CONFIRM FROM DUAL;

-- ==================================
-- 第一部分: 删除用户ID增强相关索引
-- ==================================

-- 删除用户ID相关索引
BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_CREATOR_ID_TYPE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_PRIVATE_TEMPLATE_QUERY_ID';  
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TAG_USER_ID';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

-- 删除其他优化索引
BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_TYPE_ACTIVE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_CREATOR_TYPE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_USAGE_TIME';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_NAME_SEARCH';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_ACTIVE_FLAG';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TAG_NAME_USAGE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TAG_NAME_QUERY';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_TAG_REL_TEMPLATE';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_TEMPLATE_TAG_REL_TAG';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_PUBLIC_TEMPLATE_QUERY';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP INDEX IDX_PRIVATE_TEMPLATE_QUERY';
EXCEPTION WHEN OTHERS THEN NULL; END;
/

-- ==================================
-- 第二部分: 删除用户ID字段
-- ==================================

-- 1. 删除booking_template表的用户ID字段
ALTER TABLE booking_template DROP COLUMN create_by_id;
ALTER TABLE booking_template DROP COLUMN update_by_id;

-- 2. 删除booking_template_tag表的用户ID字段
ALTER TABLE booking_template_tag DROP COLUMN create_by_id;
ALTER TABLE booking_template_tag DROP COLUMN update_by_id;

-- ==================================
-- 第三部分: 验证回滚结果
-- ==================================

-- 验证用户ID字段是否已删除
SELECT 
    table_name,
    column_name,
    'COLUMN_DROPPED' AS status
FROM user_tab_columns 
WHERE table_name IN ('BOOKING_TEMPLATE', 'BOOKING_TEMPLATE_TAG')
    AND column_name IN ('CREATE_BY_ID', 'UPDATE_BY_ID');

-- 如果上述查询无结果，说明用户ID字段已成功删除
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '用户ID字段已成功删除!'
        ELSE '删除失败，仍有' || COUNT(*) || '个用户ID字段存在'
    END AS RESULT
FROM user_tab_columns 
WHERE table_name IN ('BOOKING_TEMPLATE', 'BOOKING_TEMPLATE_TAG')
    AND column_name IN ('CREATE_BY_ID', 'UPDATE_BY_ID');

-- 验证索引删除结果
SELECT 
    index_name,
    'INDEX_DROPPED' AS status
FROM user_indexes 
WHERE index_name IN (
    'IDX_TEMPLATE_TYPE_ACTIVE',
    'IDX_TEMPLATE_CREATOR_TYPE', 
    'IDX_TEMPLATE_CREATOR_ID_TYPE',
    'IDX_TEMPLATE_USAGE_TIME',
    'IDX_TEMPLATE_NAME_SEARCH',
    'IDX_TEMPLATE_ACTIVE_FLAG',
    'IDX_TAG_NAME_USAGE',
    'IDX_TAG_NAME_QUERY',
    'IDX_TAG_USER_ID',
    'IDX_TEMPLATE_TAG_REL_TEMPLATE',
    'IDX_TEMPLATE_TAG_REL_TAG',
    'IDX_PUBLIC_TEMPLATE_QUERY',
    'IDX_PRIVATE_TEMPLATE_QUERY',
    'IDX_PRIVATE_TEMPLATE_QUERY_ID'
);

-- 如果上述查询无结果，说明索引已成功删除
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '所有相关索引已成功删除!'
        ELSE '删除失败，仍有' || COUNT(*) || '个索引存在'
    END AS RESULT
FROM user_indexes 
WHERE index_name IN (
    'IDX_TEMPLATE_TYPE_ACTIVE',
    'IDX_TEMPLATE_CREATOR_TYPE', 
    'IDX_TEMPLATE_CREATOR_ID_TYPE',
    'IDX_TEMPLATE_USAGE_TIME',
    'IDX_TEMPLATE_NAME_SEARCH',
    'IDX_TEMPLATE_ACTIVE_FLAG',
    'IDX_TAG_NAME_USAGE',
    'IDX_TAG_NAME_QUERY',
    'IDX_TAG_USER_ID',
    'IDX_TEMPLATE_TAG_REL_TEMPLATE',
    'IDX_TEMPLATE_TAG_REL_TAG',
    'IDX_PUBLIC_TEMPLATE_QUERY',
    'IDX_PRIVATE_TEMPLATE_QUERY',
    'IDX_PRIVATE_TEMPLATE_QUERY_ID'
);

-- 执行完成提示
SELECT '用户ID字段增强和索引优化回滚完成!' AS RESULT FROM DUAL;