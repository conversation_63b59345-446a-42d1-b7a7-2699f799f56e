-- ====================================
-- 订舱模板功能 - 默认数据删除回滚脚本
-- 创建时间: 2025-08-04
-- 版本: v2.0 (基于优化版数据)
-- 描述: 删除订舱模板功能的默认数据(回滚操作)
-- 警告: 此操作将删除所有默认模板和标签数据!
-- 适用脚本: 003_insert_default_data_optimized.sql
-- ====================================

-- 删除前确认提示
SELECT '警告: 即将删除订舱模板功能的所有默认数据!' AS WARNING FROM DUAL;
SELECT '如果确认要执行，请继续运行下面的删除语句' AS CONFIRM FROM DUAL;

-- ==================================
-- 第一部分: 删除模板标签关联关系
-- ==================================

-- 删除默认模板的标签关联
DELETE FROM booking_template_tag_rel 
WHERE template_id IN (
    SELECT id FROM booking_template 
    WHERE create_by = 'system' 
    AND template_name IN ('海运出口标准模板', '陆运内贸模板', '进口清关模板')
);

-- ==================================
-- 第二部分: 删除默认模板
-- ==================================

-- 删除系统创建的默认模板
DELETE FROM booking_template 
WHERE create_by = 'system' 
AND template_name IN ('海运出口标准模板', '陆运内贸模板', '进口清关模板');

-- 或者通过ID删除(如果知道具体ID)
-- DELETE FROM booking_template WHERE id IN ('BT_TEMPLATE_001', 'BT_TEMPLATE_002', 'BT_TEMPLATE_003');

-- ==================================
-- 第三部分: 删除默认标签
-- ==================================

-- 删除系统创建的默认标签
DELETE FROM booking_template_tag 
WHERE create_by = 'system' 
AND tag_name IN ('海运', '陆运', '出口', '进口', '内贸', '集装箱', '常用', '重要', '紧急', '标准', '快速', '特殊');

-- 或者通过ID删除(如果知道具体ID)
-- DELETE FROM booking_template_tag WHERE id LIKE 'BT_TAG_%';

-- ==================================
-- 第四部分: 验证删除结果
-- ==================================

-- 验证模板删除结果
SELECT 
    COUNT(*) AS remaining_templates,
    '剩余系统模板数量' AS description
FROM booking_template 
WHERE create_by = 'system';

-- 验证标签删除结果  
SELECT 
    COUNT(*) AS remaining_tags,
    '剩余系统标签数量' AS description
FROM booking_template_tag 
WHERE create_by = 'system';

-- 验证关联关系删除结果
SELECT 
    COUNT(*) AS remaining_relations,
    '剩余标签关联数量' AS description
FROM booking_template_tag_rel;

-- 详细验证 - 显示剩余的系统数据
SELECT 
    'TEMPLATE' AS data_type,
    template_name AS name,
    create_by,
    create_time
FROM booking_template 
WHERE create_by = 'system'
UNION ALL
SELECT 
    'TAG' AS data_type,
    tag_name AS name,
    create_by,
    create_time
FROM booking_template_tag 
WHERE create_by = 'system';

-- 最终结果提示
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM booking_template WHERE create_by = 'system') = 0
             AND (SELECT COUNT(*) FROM booking_template_tag WHERE create_by = 'system') = 0
             AND (SELECT COUNT(*) FROM booking_template_tag_rel) = 0
        THEN '所有默认数据已成功删除!'
        ELSE '删除不完整，请检查剩余数据'
    END AS RESULT
FROM DUAL;

-- 执行完成提示
SELECT '订舱模板功能默认数据删除回滚完成!' AS RESULT FROM DUAL;