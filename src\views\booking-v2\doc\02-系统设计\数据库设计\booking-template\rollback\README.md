# 订舱模板功能 - 回滚脚本说明

## 概述

本目录包含订舱模板功能的完整回滚脚本，用于在需要时完全清理或部分回滚相关数据库结构和数据。

## 脚本列表

### 1. `001_drop_tables.sql` - 完整表删除回滚
- **用途**: 完全删除订舱模板功能的所有数据表
- **影响**: 删除所有表、索引、约束和数据
- **对应脚本**: `001_create_tables_optimized.sql`
- **⚠️ 警告**: 此操作不可逆，将丢失所有模板数据

**包含操作**:
- 删除所有优化版索引 (14个)
- 删除模板标签关联表 `booking_template_tag_rel`
- 删除模板标签表 `booking_template_tag`  
- 删除订舱模板主表 `booking_template`
- 验证删除结果

### 2. `002_drop_user_id_and_indexes.sql` - 用户ID字段和索引回滚
- **用途**: 回滚用户ID字段增强和索引优化操作
- **影响**: 删除用户ID字段和相关索引，保留表结构和数据
- **对应脚本**: `002_add_user_id_and_indexes_merged.sql`
- **⚠️ 注意**: 回滚后将失去基于用户ID的精确权限控制

**包含操作**:
- 删除用户ID相关索引 (3个)
- 删除其他优化索引 (11个)
- 删除 `create_by_id` 和 `update_by_id` 字段
- 验证回滚结果

### 3. `003_delete_default_data.sql` - 默认数据删除回滚
- **用途**: 删除系统预置的默认模板和标签数据
- **影响**: 清理默认数据，保留表结构和用户数据
- **对应脚本**: `003_insert_default_data_optimized.sql`
- **⚠️ 注意**: 只删除 `create_by = 'system'` 的数据

**包含操作**:
- 删除默认模板标签关联关系
- 删除系统默认模板 (3个)
- 删除系统默认标签 (12个)
- 验证删除结果

## 使用指南

### 执行顺序建议

**完整回滚顺序** (从最新到最早):
```sql
-- 3. 首先删除默认数据 (可选)
@003_delete_default_data.sql

-- 2. 然后回滚用户ID字段增强 (可选)  
@002_drop_user_id_and_indexes.sql

-- 1. 最后完全删除表结构 (危险操作)
@001_drop_tables.sql
```

**部分回滚选项**:
- 只清理默认数据: 执行 `003_delete_default_data.sql`
- 回滚用户ID增强: 执行 `002_drop_user_id_and_indexes.sql`
- 完全删除功能: 执行 `001_drop_tables.sql`

### 安全检查

**执行前检查**:
```sql
-- 检查当前模板数量
SELECT COUNT(*) AS template_count FROM booking_template;

-- 检查用户数据
SELECT create_by, COUNT(*) FROM booking_template 
WHERE create_by != 'system' GROUP BY create_by;

-- 检查表空间使用情况
SELECT table_name, num_rows FROM user_tables 
WHERE table_name LIKE '%TEMPLATE%';
```

**备份建议**:
```sql
-- 数据备份 (在回滚前执行)
CREATE TABLE booking_template_backup AS SELECT * FROM booking_template;
CREATE TABLE booking_template_tag_backup AS SELECT * FROM booking_template_tag;
CREATE TABLE booking_template_tag_rel_backup AS SELECT * FROM booking_template_tag_rel;
```

### 验证方法

每个脚本都包含完整的验证查询:

- **表删除验证**: 检查 `user_tables` 中是否还存在相关表
- **字段删除验证**: 检查 `user_tab_columns` 中是否还存在用户ID字段  
- **索引删除验证**: 检查 `user_indexes` 中是否还存在相关索引
- **数据删除验证**: 统计剩余的系统数据数量

### 恢复方法

如果需要恢复已回滚的功能:

1. **恢复表结构**: 重新执行对应的创建脚本
2. **恢复数据**: 从备份表中恢复数据
3. **重建索引**: 重新执行索引创建脚本
4. **验证功能**: 测试模板功能是否正常

## 注意事项

### ⚠️ 风险警告

1. **数据丢失风险**: 回滚操作可能导致用户数据永久丢失
2. **依赖影响**: 删除表可能影响依赖此功能的其他模块
3. **权限变化**: 回滚用户ID字段会影响数据权限控制精度
4. **性能影响**: 删除索引会影响查询性能

### 💡 最佳实践

1. **测试环境先行**: 在测试环境先验证回滚脚本
2. **完整备份**: 回滚前进行完整的数据备份
3. **分阶段执行**: 建议分阶段执行，逐步回滚
4. **监控影响**: 关注回滚对系统性能和功能的影响
5. **文档记录**: 记录回滚原因和过程，便于后续分析

### 🔧 故障排查

**常见问题**:

1. **外键约束错误**: 使用 `CASCADE CONSTRAINTS` 选项
2. **索引不存在错误**: 使用 `EXCEPTION WHEN OTHERS THEN NULL` 忽略
3. **权限不足错误**: 确保执行用户有足够的权限
4. **会话锁定**: 确保没有其他会话正在使用相关表

**回滚失败处理**:
```sql
-- 强制解锁表
ALTER SYSTEM KILL SESSION 'sid,serial#';

-- 检查锁定情况  
SELECT * FROM v$locked_object;

-- 手动清理残留对象
DROP INDEX index_name CASCADE;
DROP TABLE table_name CASCADE CONSTRAINTS PURGE;
```

## 版本兼容性

- **数据库版本**: Oracle 11g+
- **脚本版本**: v2.0 (对应优化版表结构)
- **框架兼容**: 若依框架 + MyBatis-Flex
- **更新日期**: 2025-08-04