@echo off
echo ========================================
echo 测试Bean冲突修复
echo ========================================

echo 1. 清理编译文件...
call mvn clean -q

echo 2. 编译项目...
call mvn compile -q

if %ERRORLEVEL% EQU 0 (
    echo ✅ 编译成功！Bean冲突已修复
    echo.
    echo 现在可以启动应用：
    echo   - 使用 run-with-heap.bat 启动（推荐）
    echo   - 或使用 mvn spring-boot:run
) else (
    echo ❌ 编译失败，请检查错误信息
)

echo ========================================
pause 