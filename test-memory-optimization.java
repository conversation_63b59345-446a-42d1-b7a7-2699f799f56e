import java.util.*;

/**
 * 内存优化测试类
 * 用于验证密码检查的内存优化是否有效
 */
public class TestMemoryOptimization {
    
    // 模拟替换映射
    private static final Map<Character, String> REPLACEMENT_MAP = new HashMap<>();
    static {
        REPLACEMENT_MAP.put('a', "@");
        REPLACEMENT_MAP.put('o', "0");
        REPLACEMENT_MAP.put('s', "5");
    }
    
    // 模拟基础禁止单词集合
    private static final Set<String> BASE_FORBIDDEN_WORDS = new HashSet<>(Arrays.asList(
        "password", "admin", "test", "hello", "world"
    ));
    
    /**
     * 生成输入字符串的所有可能替换组合
     */
    public static List<String> generateReplacements(String input) {
        List<String> result = new ArrayList<>();
        if (input == null || input.isEmpty()) {
            result.add("");
            return result;
        }
        generateHelper(input, 0, "", result);
        return result;
    }
    
    /**
     * 递归辅助函数，构建所有替换组合
     */
    private static void generateHelper(String input, int index, String current, List<String> result) {
        if (index == input.length()) {
            result.add(current);
            return;
        }
        
        char c = input.charAt(index);
        if (REPLACEMENT_MAP.containsKey(c)) {
            // 分支1：不替换，添加原字符
            generateHelper(input, index + 1, current + c, result);
            // 分支2：替换，添加替换字符串
            String replacement = REPLACEMENT_MAP.get(c);
            generateHelper(input, index + 1, current + replacement, result);
        } else {
            // 不可替换字符，直接添加
            generateHelper(input, index + 1, current + c, result);
        }
    }
    
    /**
     * 验证密码（优化后的方法）
     */
    public static List<String> validatePassword(String password) {
        List<String> reasons = new ArrayList<>();
        if (password == null || password.isEmpty()) return reasons;

        String lowerPwd = password.toLowerCase();
        
        // 生成输入密码的所有替换组合
        List<String> passwordReplacements = generateReplacements(lowerPwd);
        
        // 检查密码及其替换组合是否包含禁止单词（找到第一个就停止）
        String firstMatchedWord = null;
        
        // 检查原始密码
        for (String forbiddenWord : BASE_FORBIDDEN_WORDS) {
            if (lowerPwd.contains(forbiddenWord)) {
                firstMatchedWord = forbiddenWord;
                break;
            }
        }
        
        // 如果原始密码中没有找到，检查密码的替换组合
        if (firstMatchedWord == null) {
            for (String replacement : passwordReplacements) {
                for (String forbiddenWord : BASE_FORBIDDEN_WORDS) {
                    if (replacement.contains(forbiddenWord)) {
                        firstMatchedWord = forbiddenWord;
                        break;
                    }
                }
                // 如果找到了匹配的单词，跳出外层循环
                if (firstMatchedWord != null) {
                    break;
                }
            }
        }
        
        if (firstMatchedWord != null) {
            reasons.add("密码包含禁止的单词: " + firstMatchedWord);
        }
        
        return reasons;
    }
    
    public static void main(String[] args) {
        System.out.println("=== 内存优化测试 ===");
        
        // 测试用例
        String[] testPasswords = {
            "password123",
            "admin@123", 
            "test0word",
            "hell0w0rld",
            "securepass"
        };
        
        for (String password : testPasswords) {
            System.out.println("\n测试密码: " + password);
            
            // 生成替换组合
            List<String> replacements = generateReplacements(password.toLowerCase());
            System.out.println("替换组合数量: " + replacements.size());
            System.out.println("替换组合: " + replacements);
            
            // 验证密码
            List<String> reasons = validatePassword(password);
            if (reasons.isEmpty()) {
                System.out.println("结果: 密码强度足够");
            } else {
                System.out.println("结果: " + String.join("; ", reasons));
            }
        }
        
        System.out.println("\n=== 内存优化测试完成 ===");
        System.out.println("优化说明:");
        System.out.println("1. 不再预生成所有单词的替换组合");
        System.out.println("2. 只在需要时生成输入密码的替换组合");
        System.out.println("3. 大幅减少内存使用，避免OutOfMemoryError");
        System.out.println("4. 找到第一个禁止单词就停止检测，提高性能");
    }
} 