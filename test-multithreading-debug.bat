@echo off
echo ========================================
echo 多线程密码检查调试测试
echo ========================================
echo.

echo 1. 清理并编译项目...
call mvn clean compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 2. 启动应用程序...
start "RuoYi Application" cmd /k "mvn spring-boot:run"

echo.
echo 3. 等待应用程序启动（30秒）...
timeout /t 30 /nobreak > nul

echo.
echo 4. 检查系统状态...
curl -X GET "http://localhost:8080/system/password/performance/debug" ^
  --connect-timeout 10

echo.
echo 5. 测试密码检查API...
echo 测试简单密码检查...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=test123456" ^
  --connect-timeout 10

echo.
echo 测试包含禁止单词的密码...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=password123" ^
  --connect-timeout 10

echo.
echo 测试复杂密码检查...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=sh1pp1ng123" ^
  --connect-timeout 10

echo.
echo 6. 检查应用程序日志...
echo 请查看应用程序控制台输出，寻找以下信息：
echo - "密码检查并行处理异常"
echo - "异常堆栈:"
echo - "回退到串行处理"
echo - 任何其他异常信息
echo.

echo 7. 如果看到null异常，请检查：
echo - Redis连接是否正常
echo - 禁止单词文件是否正确加载
echo - 线程池是否正常工作
echo.

echo 测试完成。请检查应用程序日志以获取详细错误信息。
pause 