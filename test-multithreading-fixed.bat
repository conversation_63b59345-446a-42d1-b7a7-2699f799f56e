@echo off
echo ========================================
echo 多线程密码检查功能测试脚本（修复版）
echo ========================================
echo.

echo 1. 清理并编译项目...
call mvn clean compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)
echo 编译成功！
echo.

echo 2. 启动应用程序...
echo 使用优化的JVM参数启动应用...
start "Spring Boot Application" cmd /k "mvn spring-boot:run -Dspring-boot.run.jvmArguments=\"-Xms1g -Xmx2g -XX:+UseG1GC\""

echo 等待应用启动...
timeout /t 30 /nobreak > nul

echo 3. 测试多线程密码检查功能...
echo.

echo 测试单个密码检查（并行处理）：
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/json" ^
  -d "{\"password\":\"test123456\",\"username\":\"testuser\",\"phone\":\"13800138000\"}"
echo.
echo.

echo 测试批量密码检查（并行处理）：
curl -X POST "http://localhost:8080/system/password/performance/test-batch" ^
  -H "Content-Type: application/json" ^
  -d "{\"passwords\":[\"test123456\",\"password123\",\"admin123\",\"123456789\",\"qwerty123\"]}"
echo.
echo.

echo 测试并发密码检查（多线程并行）：
curl -X POST "http://localhost:8080/system/password/performance/test-concurrent" ^
  -H "Content-Type: application/json" ^
  -d "{\"password\":\"test123456\",\"username\":\"testuser\",\"phone\":\"13800138000\",\"concurrentCount\":5}"
echo.
echo.

echo 4. 检查日志中的异常信息...
echo 请查看控制台输出，确认是否还有以下异常：
echo - "密码检查并行处理异常: null"
echo - "并行密码检查异常，回退到串行处理: null"
echo.

echo 5. 性能对比说明：
echo 修复后的多线程优化包括：
echo - 避免嵌套并行处理（validatePassword 内部不再使用并行）
echo - 增强异常处理，每个异步方法都有 try-catch
echo - 更详细的异常日志，便于调试
echo - 串行版本作为备用方案
echo.

echo 6. 预期性能提升：
echo - 单个密码检查：从 10+ 秒降低到 1-3 秒
echo - 批量密码检查：并行处理，总时间显著减少
echo - 并发密码检查：多线程同时处理，吞吐量提升
echo.

echo 测试完成！请检查上述API响应和日志输出。
echo 如果仍有异常，请查看具体的异常信息进行进一步调试。
pause 