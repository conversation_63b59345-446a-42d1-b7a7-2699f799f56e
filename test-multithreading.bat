@echo off
echo ========================================
echo 密码检查多线程并行处理性能测试
echo ========================================
echo.

echo 正在编译项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo 编译失败，请检查代码错误
    pause
    exit /b 1
)

echo 编译成功！
echo.

echo 启动应用进行多线程性能测试...
echo.
echo 测试说明：
echo 1. 应用启动后，访问以下API进行性能测试：
echo    - 单次测试: POST /system/password/performance/test-single
echo    - 批量测试: POST /system/password/performance/test-batch?count=20
echo    - 并发测试: POST /system/password/performance/test-concurrent?count=20
echo.
echo 2. 预期性能提升：
echo    - 单次检查: 从10秒+降低到1-3秒
echo    - 批量检查: 并发执行，总时间显著减少
echo    - 多线程优化: 充分利用多核CPU性能
echo.
echo 3. 观察要点：
echo    - 控制台输出的密码检查耗时
echo    - 是否有并行处理异常信息
echo    - 线程池使用情况
echo.

call mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xms1g -Xmx2g -XX:+UseG1GC"

pause 