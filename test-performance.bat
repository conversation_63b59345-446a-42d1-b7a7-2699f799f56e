@echo off
echo 正在测试密码检查多线程并行处理性能优化...
echo.

echo 多线程优化说明：
echo - 使用 CompletableFuture 并行执行不同类型的密码检查
echo - 线程池大小：%NUMBER_OF_PROCESSORS% 个线程（根据CPU核心数自动调整）
echo - 并行检查项目：
echo   * 禁止单词检查
echo   * 键盘模式检查  
echo   * 日期模式检查
echo   * 用户信息检查（用户名、手机号）
echo   * 默认密码检查
echo   * 密码字典检查
echo - 超时机制：每个检查任务最多等待5秒
echo - 异常处理：如果并行处理失败，自动回退到串行处理
echo.

echo 编译项目...
call mvn clean compile -q

echo.
echo 启动应用进行性能测试...
echo 请观察控制台输出，查看密码检查耗时是否从10秒以上降低到毫秒级别
echo 多线程优化应该能显著提升百万级数据比较的性能
echo.

call mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xms1g -Xmx2g"

pause 