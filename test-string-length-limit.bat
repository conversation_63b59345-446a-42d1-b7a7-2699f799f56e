@echo off
echo ========================================
echo 字符串长度限制功能测试
echo ========================================
echo.

echo 1. 清理并编译项目...
call mvn clean compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 2. 启动应用程序...
start "RuoYi Application" cmd /k "mvn spring-boot:run"

echo.
echo 3. 等待应用程序启动（30秒）...
timeout /t 30 /nobreak > nul

echo.
echo 4. 测试短密码（无长度限制）...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=shipping123" ^
  --connect-timeout 15

echo.
echo 5. 测试中等长度密码...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=shippingcompany123" ^
  --connect-timeout 15

echo.
echo 6. 测试长密码（超过20位，验证长度限制）...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=shippingcompany123456789" ^
  --connect-timeout 15

echo.
echo 7. 测试超长密码（验证长度限制效果）...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=shippingcompanyinternational123456789" ^
  --connect-timeout 15

echo.
echo 8. 检查系统状态...
curl -X GET "http://localhost:8080/system/password/performance/debug" ^
  --connect-timeout 10

echo.
echo 9. 检查应用程序日志...
echo 请查看应用程序控制台输出，寻找以下信息：
echo - "替换组合检查将限制字符串长度为20位"
echo - "串行密码验证完成，耗时:"
echo - 任何与字符串长度相关的日志信息
echo.
echo 验证要点：
echo - 长密码的检查时间是否合理
echo - 是否出现字符串长度限制相关的日志
echo - 检查结果是否正确
echo.
echo 测试完成。请检查应用程序日志以验证字符串长度限制功能。
pause 