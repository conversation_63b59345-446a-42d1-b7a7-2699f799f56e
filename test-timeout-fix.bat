@echo off
echo ========================================
echo 密码检查超时修复测试
echo ========================================
echo.

echo 1. 清理并编译项目...
call mvn clean compile
if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 2. 启动应用程序...
start "RuoYi Application" cmd /k "mvn spring-boot:run"

echo.
echo 3. 等待应用程序启动（30秒）...
timeout /t 30 /nobreak > nul

echo.
echo 4. 测试简单密码检查...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=test123456" ^
  --connect-timeout 15

echo.
echo 5. 测试包含可替换字符的密码（可能触发递归）...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=shipping123" ^
  --connect-timeout 15

echo.
echo 6. 测试复杂密码检查...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=sh1pp1ng123" ^
  --connect-timeout 15

echo.
echo 7. 测试长密码检查（验证字符串长度限制）...
curl -X POST "http://localhost:8080/system/password/performance/test-single" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "password=shippingcompany123456789" ^
  --connect-timeout 15

echo.
echo 8. 检查系统状态...
curl -X GET "http://localhost:8080/system/password/performance/debug" ^
  --connect-timeout 10

echo.
echo 8. 检查应用程序日志...
echo 请查看应用程序控制台输出，寻找以下信息：
echo - "开始并行密码检查，超时时间: 1000秒"
echo - "开始检查密码字典（最耗时的步骤）..."
echo - "密码字典检查完成"
echo - "开始串行密码验证，密码长度:"
echo - "密码包含可替换字符数量:"
echo - "替换组合检查将限制字符串长度为20位"
echo - "串行密码验证完成，耗时:"
echo - 任何超时或异常信息
echo.
echo 如果看到超时异常，请检查：
echo - 密码是否包含太多可替换字符
echo - 递归深度是否超过限制（20层）
echo - 禁止单词集合是否过大
echo - 字符串长度是否被正确限制在20位以内
echo.
echo 测试完成。请检查应用程序日志以获取详细性能信息。
pause 